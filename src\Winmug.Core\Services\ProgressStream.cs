using System.Diagnostics;

namespace Winmug.Core.Services;

/// <summary>
/// Stream wrapper that reports download progress (alias for ProgressReportingStream)
/// </summary>
internal class ProgressStream : Stream
{
    private readonly Stream _innerStream;
    private readonly long? _totalBytes;
    private readonly IProgress<DownloadProgress> _progress;
    private readonly Stopwatch _stopwatch;
    private long _bytesRead;

    public ProgressStream(Stream innerStream, long totalBytes, IProgress<DownloadProgress> progress)
    {
        _innerStream = innerStream;
        _totalBytes = totalBytes;
        _progress = progress;
        _stopwatch = Stopwatch.StartNew();
    }

    public override bool CanRead => _innerStream.CanRead;
    public override bool CanSeek => _innerStream.CanSeek;
    public override bool CanWrite => _innerStream.CanWrite;
    public override long Length => _innerStream.Length;
    public override long Position { get => _innerStream.Position; set => _innerStream.Position = value; }

    public override int Read(byte[] buffer, int offset, int count)
    {
        var bytesRead = _innerStream.Read(buffer, offset, count);
        ReportProgress(bytesRead);
        return bytesRead;
    }

    public override async Task<int> ReadAsync(byte[] buffer, int offset, int count, CancellationToken cancellationToken)
    {
        var bytesRead = await _innerStream.ReadAsync(buffer, offset, count, cancellationToken);
        ReportProgress(bytesRead);
        return bytesRead;
    }

    private void ReportProgress(int bytesRead)
    {
        _bytesRead += bytesRead;
        var elapsed = _stopwatch.Elapsed;
        var bytesPerSecond = elapsed.TotalSeconds > 0 ? _bytesRead / elapsed.TotalSeconds : 0;
        
        TimeSpan? estimatedTimeRemaining = null;
        if (_totalBytes.HasValue && bytesPerSecond > 0)
        {
            var remainingBytes = _totalBytes.Value - _bytesRead;
            estimatedTimeRemaining = TimeSpan.FromSeconds(remainingBytes / bytesPerSecond);
        }

        _progress.Report(new DownloadProgress
        {
            BytesDownloaded = _bytesRead,
            TotalBytes = _totalBytes,
            Elapsed = elapsed,
            BytesPerSecond = bytesPerSecond,
            EstimatedTimeRemaining = estimatedTimeRemaining
        });
    }

    public override void Flush() => _innerStream.Flush();
    public override Task FlushAsync(CancellationToken cancellationToken) => _innerStream.FlushAsync(cancellationToken);
    public override long Seek(long offset, SeekOrigin origin) => _innerStream.Seek(offset, origin);
    public override void SetLength(long value) => _innerStream.SetLength(value);
    public override void Write(byte[] buffer, int offset, int count) => _innerStream.Write(buffer, offset, count);
    public override Task WriteAsync(byte[] buffer, int offset, int count, CancellationToken cancellationToken) => _innerStream.WriteAsync(buffer, offset, count, cancellationToken);

    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            _innerStream?.Dispose();
        }
        base.Dispose(disposing);
    }

    public override async ValueTask DisposeAsync()
    {
        if (_innerStream != null)
        {
            await _innerStream.DisposeAsync();
        }
        await base.DisposeAsync();
    }
}
