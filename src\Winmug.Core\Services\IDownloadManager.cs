using Winmug.Core.Models;

namespace Winmug.Core.Services;

/// <summary>
/// Manages the download of photos from SmugMug
/// </summary>
public interface IDownloadManager
{
    /// <summary>
    /// Event raised when download progress is updated
    /// </summary>
    event EventHandler<DownloadProgressEventArgs>? ProgressUpdated;

    /// <summary>
    /// Event raised when download status changes
    /// </summary>
    event EventHandler<DownloadStatusChangedEventArgs>? StatusChanged;

    /// <summary>
    /// Event raised when an error occurs during download
    /// </summary>
    event EventHandler<DownloadErrorEventArgs>? ErrorOccurred;

    /// <summary>
    /// Gets the current download status
    /// </summary>
    DownloadStatus Status { get; }

    /// <summary>
    /// Gets the current download statistics
    /// </summary>
    DownloadStatistics Statistics { get; }

    /// <summary>
    /// Starts downloading all photos from the user's SmugMug account
    /// </summary>
    /// <param name="targetDirectory">Local directory to save photos</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task StartDownloadAsync(string targetDirectory, CancellationToken cancellationToken = default);

    /// <summary>
    /// Starts downloading photos from selected albums only
    /// </summary>
    /// <param name="targetDirectory">Local directory to save photos</param>
    /// <param name="selectedAlbums">List of albums to download</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task StartSelectiveDownloadAsync(string targetDirectory, List<SelectableAlbum> selectedAlbums, CancellationToken cancellationToken = default);

    /// <summary>
    /// Pauses the current download operation
    /// </summary>
    Task PauseAsync();

    /// <summary>
    /// Resumes a paused download operation
    /// </summary>
    Task ResumeAsync();

    /// <summary>
    /// Cancels the current download operation
    /// </summary>
    Task CancelAsync();

    /// <summary>
    /// Gets the download summary after completion
    /// </summary>
    DownloadSummary GetDownloadSummary();
}

/// <summary>
/// Represents the current status of a download operation
/// </summary>
public enum DownloadStatus
{
    NotStarted,
    Discovering,
    Downloading,
    Paused,
    Completed,
    Cancelled,
    Error
}

/// <summary>
/// Contains statistics about the current download operation
/// </summary>
public class DownloadStatistics
{
    public int TotalAlbums { get; set; }
    public int ProcessedAlbums { get; set; }
    public int TotalPhotos { get; set; }
    public int DownloadedPhotos { get; set; }
    public int FailedPhotos { get; set; }
    public long TotalBytes { get; set; }
    public long DownloadedBytes { get; set; }
    public double OverallProgressPercentage => TotalPhotos > 0 ? (double)DownloadedPhotos / TotalPhotos * 100 : 0;
    public TimeSpan ElapsedTime { get; set; }
    public double? AverageDownloadSpeed { get; set; }
    public TimeSpan? EstimatedTimeRemaining { get; set; }
}

/// <summary>
/// Event arguments for download progress updates
/// </summary>
public class DownloadProgressEventArgs : EventArgs
{
    public DownloadStatistics Statistics { get; }
    public string? CurrentOperation { get; }
    public SmugMugImage? CurrentImage { get; }

    public DownloadProgressEventArgs(DownloadStatistics statistics, string? currentOperation = null, SmugMugImage? currentImage = null)
    {
        Statistics = statistics;
        CurrentOperation = currentOperation;
        CurrentImage = currentImage;
    }
}

/// <summary>
/// Event arguments for download status changes
/// </summary>
public class DownloadStatusChangedEventArgs : EventArgs
{
    public DownloadStatus OldStatus { get; }
    public DownloadStatus NewStatus { get; }
    public string? Message { get; }

    public DownloadStatusChangedEventArgs(DownloadStatus oldStatus, DownloadStatus newStatus, string? message = null)
    {
        OldStatus = oldStatus;
        NewStatus = newStatus;
        Message = message;
    }
}

/// <summary>
/// Event arguments for download errors
/// </summary>
public class DownloadErrorEventArgs : EventArgs
{
    public Exception Exception { get; }
    public string? Context { get; }
    public SmugMugImage? Image { get; }
    public SmugMugAlbum? Album { get; }

    public DownloadErrorEventArgs(Exception exception, string? context = null, SmugMugImage? image = null, SmugMugAlbum? album = null)
    {
        Exception = exception;
        Context = context;
        Image = image;
        Album = album;
    }
}

/// <summary>
/// Summary of a completed download operation
/// </summary>
public class DownloadSummary
{
    public DownloadStatus FinalStatus { get; set; }
    public DownloadStatistics Statistics { get; set; } = new();
    public List<DownloadError> Errors { get; set; } = new();
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public TimeSpan TotalDuration => EndTime - StartTime;
}

/// <summary>
/// Represents an error that occurred during download
/// </summary>
public class DownloadError
{
    public string Message { get; set; } = string.Empty;
    public string? Context { get; set; }
    public string? ImageKey { get; set; }
    public string? ImageTitle { get; set; }
    public string? AlbumKey { get; set; }
    public string? AlbumName { get; set; }
    public DateTime Timestamp { get; set; }
    public Exception? Exception { get; set; }
}
