using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace Winmug.Converters
{
    /// <summary>
    /// Converts null values to Visibility. Returns Visible if value is null, Collapsed otherwise.
    /// Used for showing indeterminate state in checkboxes.
    /// </summary>
    public class NullToVisibilityConverter : IValueConverter
    {
        public static readonly NullToVisibilityConverter Instance = new();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value == null ? Visibility.Visible : Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
