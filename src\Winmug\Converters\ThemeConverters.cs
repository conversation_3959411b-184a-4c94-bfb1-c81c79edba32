using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace Winmug.Converters;

/// <summary>
/// Converter for theme icon display
/// </summary>
public class ThemeIconConverter : IValueConverter
{
    public static readonly ThemeIconConverter Instance = new();

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool isDarkTheme)
        {
            return isDarkTheme ? "🌙" : "☀️";
        }
        return "🌙";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Converter for theme tooltip text
/// </summary>
public class ThemeTooltipConverter : IValueConverter
{
    public static readonly ThemeTooltipConverter Instance = new();

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool isDarkTheme)
        {
            return isDarkTheme ? "Switch to Light Theme" : "Switch to Dark Theme";
        }
        return "Switch to Light Theme";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Converter for theme-based shadow colors
/// </summary>
public class ThemeColorConverter : IValueConverter
{
    public static readonly ThemeColorConverter Instance = new();

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool isDarkTheme)
        {
            return isDarkTheme ? Color.FromRgb(0, 0, 0) : Color.FromRgb(224, 224, 224);
        }
        return Color.FromRgb(0, 0, 0);
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Converter for tree view toggle button text
/// </summary>
public class TreeViewToggleConverter : IValueConverter
{
    public static readonly TreeViewToggleConverter Instance = new();

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool useTreeView)
        {
            return useTreeView ? "🌳 Tree View" : "📋 List View";
        }
        return "🌳 Tree View";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

/// <summary>
/// Converter for tree view toggle button tooltip
/// </summary>
public class TreeViewTooltipConverter : IValueConverter
{
    public static readonly TreeViewTooltipConverter Instance = new();

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool useTreeView)
        {
            return useTreeView ? "Switch to flat list view" : "Switch to hierarchical tree view";
        }
        return "Switch to flat list view";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
