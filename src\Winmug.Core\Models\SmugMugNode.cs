using System.Text.Json.Serialization;

namespace Winmug.Core.Models;

/// <summary>
/// Represents a SmugMug node (folder, album, or page)
/// </summary>
public class SmugMugNode
{
    [JsonPropertyName("NodeID")]
    public string NodeId { get; set; } = string.Empty;

    [JsonPropertyName("Type")]
    public string Type { get; set; } = string.Empty;

    [JsonPropertyName("Name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("Description")]
    public string? Description { get; set; }

    [JsonPropertyName("UrlName")]
    public string? UrlName { get; set; }

    [JsonPropertyName("WebUri")]
    public string WebUri { get; set; } = string.Empty;

    [JsonPropertyName("Uri")]
    public string Uri { get; set; } = string.Empty;

    [JsonPropertyName("DateAdded")]
    public DateTime? DateAdded { get; set; }

    [JsonPropertyName("DateModified")]
    public DateTime? DateModified { get; set; }

    [JsonPropertyName("Privacy")]
    public string? Privacy { get; set; }

    [JsonPropertyName("SmugSearchable")]
    public string? SmugSearchable { get; set; }

    [JsonPropertyName("WorldSearchable")]
    public string? WorldSearchable { get; set; }

    [JsonPropertyName("PasswordHint")]
    public string? PasswordHint { get; set; }

    [JsonPropertyName("HasChildren")]
    public bool? HasChildren { get; set; }

    [JsonPropertyName("Uris")]
    public SmugMugNodeUris? Uris { get; set; }

    // Navigation properties for building hierarchy
    public SmugMugNode? Parent { get; set; }
    public List<SmugMugNode> Children { get; set; } = new();
    public List<SmugMugImage> Images { get; set; } = new();

    // Helper properties
    public bool IsFolder => Type.Equals("Folder", StringComparison.OrdinalIgnoreCase);
    public bool IsAlbum => Type.Equals("Album", StringComparison.OrdinalIgnoreCase);
    public string DisplayName => !string.IsNullOrEmpty(Name) ? Name : UrlName ?? NodeId;
}

/// <summary>
/// Contains URIs for SmugMug node-related resources
/// </summary>
public class SmugMugNodeUris
{
    [JsonPropertyName("ChildNodes")]
    public SmugMugUriInfo? ChildNodes { get; set; }

    [JsonPropertyName("ParentNode")]
    public SmugMugUriInfo? ParentNode { get; set; }

    [JsonPropertyName("ParentNodes")]
    public SmugMugUriInfo? ParentNodes { get; set; }

    [JsonPropertyName("User")]
    public SmugMugUriInfo? User { get; set; }

    [JsonPropertyName("Album")]
    public SmugMugUriInfo? Album { get; set; }

    [JsonPropertyName("HighlightImage")]
    public SmugMugUriInfo? HighlightImage { get; set; }
}
