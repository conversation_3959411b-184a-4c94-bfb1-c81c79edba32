using System.Text.Json.Serialization;

namespace Winmug.Core.Models;

/// <summary>
/// Represents a SmugMug folder from the legacy Folder API
/// Used with endpoints like /api/v2/folder/user/{nickname}
/// </summary>
public class SmugMugFolder
{
    [JsonPropertyName("NodeID")]
    public string? NodeId { get; set; }

    [JsonPropertyName("Type")]
    public string Type { get; set; } = string.Empty;

    [JsonPropertyName("Name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("Description")]
    public string? Description { get; set; }

    [JsonPropertyName("UrlName")]
    public string? UrlName { get; set; }

    [JsonPropertyName("WebUri")]
    public string? WebUri { get; set; }

    [JsonPropertyName("Uri")]
    public string? Uri { get; set; }

    [JsonPropertyName("DateAdded")]
    public DateTime? DateAdded { get; set; }

    [JsonPropertyName("DateModified")]
    public DateTime? DateModified { get; set; }

    [J<PERSON><PERSON>ropertyName("HasChildren")]
    public bool? HasChildren { get; set; }

    [JsonPropertyName("ChildCount")]
    public int? ChildCount { get; set; }

    [JsonPropertyName("AlbumCount")]
    public int? AlbumCount { get; set; }

    [JsonPropertyName("FolderCount")]
    public int? FolderCount { get; set; }

    [JsonPropertyName("Privacy")]
    public string? Privacy { get; set; }

    [JsonPropertyName("SmugSearchable")]
    public string? SmugSearchable { get; set; }

    [JsonPropertyName("WorldSearchable")]
    public bool? WorldSearchable { get; set; }

    [JsonPropertyName("SortMethod")]
    public string? SortMethod { get; set; }

    [JsonPropertyName("SortDirection")]
    public string? SortDirection { get; set; }

    [JsonPropertyName("Uris")]
    public SmugMugFolderUris? Uris { get; set; }

    // Helper properties
    public bool IsFolder => Type.Equals("Folder", StringComparison.OrdinalIgnoreCase);
    public string DisplayName => !string.IsNullOrEmpty(Name) ? Name : UrlName ?? NodeId ?? "Unknown";
}

/// <summary>
/// URIs available for a SmugMug folder
/// </summary>
public class SmugMugFolderUris
{
    [JsonPropertyName("Node")]
    public SmugMugUriInfo? Node { get; set; }

    [JsonPropertyName("ChildNodes")]
    public SmugMugUriInfo? ChildNodes { get; set; }

    [JsonPropertyName("ParentNode")]
    public SmugMugUriInfo? ParentNode { get; set; }

    [JsonPropertyName("User")]
    public SmugMugUriInfo? User { get; set; }

    [JsonPropertyName("Folders")]
    public SmugMugUriInfo? Folders { get; set; }

    [JsonPropertyName("Albums")]
    public SmugMugUriInfo? Albums { get; set; }

    [JsonPropertyName("FolderAlbums")]
    public SmugMugUriInfo? FolderAlbums { get; set; }

    [JsonPropertyName("HighlightImage")]
    public SmugMugUriInfo? HighlightImage { get; set; }
}

/// <summary>
/// Response from the !folderlist endpoint that returns all folders recursively
/// Based on SmugMug API documentation: /api/v2/folder/user/{nickname}/{folder}!folderlist
/// </summary>
public class SmugMugFolderListResponse
{
    [JsonPropertyName("Uri")]
    public string Uri { get; set; } = string.Empty;

    [JsonPropertyName("Locator")]
    public string? Locator { get; set; }

    [JsonPropertyName("LocatorType")]
    public string? LocatorType { get; set; }

    [JsonPropertyName("UriDescription")]
    public string? UriDescription { get; set; }

    [JsonPropertyName("EndpointType")]
    public string? EndpointType { get; set; }

    [JsonPropertyName("FolderList")]
    public List<SmugMugFolderListItem> FolderList { get; set; } = new();
}

/// <summary>
/// Individual folder item from the !folderlist response
/// </summary>
public class SmugMugFolderListItem
{
    [JsonPropertyName("Uri")]
    public string Uri { get; set; } = string.Empty;

    [JsonPropertyName("UrlPath")]
    public string? UrlPath { get; set; }

    [JsonPropertyName("Name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("CanHaveFolder")]
    public bool CanHaveFolder { get; set; }
}


