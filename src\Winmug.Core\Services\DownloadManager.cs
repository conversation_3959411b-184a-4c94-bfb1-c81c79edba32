using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;
using Winmug.Core.Models;

namespace Winmug.Core.Services;

/// <summary>
/// Manages downloading of images from SmugMug albums
/// </summary>
public class DownloadManager : IDownloadManager
{
    private readonly ILogger<DownloadManager> _logger;
    private readonly ISmugMugApiClient _apiClient;
    private DownloadStatus _status = DownloadStatus.NotStarted;
    private DownloadStatistics _statistics = new();
    private CancellationTokenSource? _cancellationTokenSource;
    private readonly Stopwatch _stopwatch = new();
    private readonly object _lockObject = new();

    public DownloadManager(ILogger<DownloadManager> logger, ISmugMugApiClient apiClient)
    {
        _logger = logger;
        _apiClient = apiClient;
    }

    // Events - placeholder implementations
    public event EventHandler<DownloadProgressEventArgs>? ProgressUpdated;
    public event EventHandler<DownloadStatusChangedEventArgs>? StatusChanged;
    public event EventHandler<DownloadErrorEventArgs>? ErrorOccurred;

    // Properties
    public DownloadStatus Status => _status;
    public DownloadStatistics Statistics => _statistics;

    // Methods - actual implementations
    public async Task StartDownloadAsync(string targetDirectory, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting download of all albums to: {TargetDirectory}", targetDirectory);

        // Get all albums first
        var allAlbums = _apiClient.GetAllUserAlbums(cancellationToken);
        var selectableAlbums = allAlbums.Select(album => new SelectableAlbum
        {
            AlbumKey = album.AlbumKey,
            Name = album.Name,
            Description = album.Description ?? string.Empty,
            UrlName = album.UrlName ?? string.Empty,
            FullPath = album.UrlPath ?? string.Empty, // Use UrlPath for folder structure
            ImageCount = album.ImageCount ?? 0,
            EstimatedSizeBytes = album.TotalSizes ?? 0,
            DateCreated = album.Date ?? DateTime.MinValue,
            DateModified = album.LastUpdated ?? DateTime.MinValue,
            Privacy = album.Privacy ?? string.Empty,
            AllowDownloads = album.AllowDownloads ?? true,
            IsSelected = true // Select all for full download
        }).ToList();

        await StartSelectiveDownloadAsync(targetDirectory, selectableAlbums, cancellationToken);
    }

    public async Task StartSelectiveDownloadAsync(string targetDirectory, List<SelectableAlbum> selectedAlbums, CancellationToken cancellationToken = default)
    {
        if (selectedAlbums == null || selectedAlbums.Count == 0)
        {
            _logger.LogWarning("No albums selected for download");
            return;
        }

        _logger.LogInformation("Starting selective download of {AlbumCount} albums to: {TargetDirectory}",
            selectedAlbums.Count, targetDirectory);

        try
        {
            _cancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            var token = _cancellationTokenSource.Token;

            // Initialize statistics
            lock (_lockObject)
            {
                _status = DownloadStatus.Discovering;
                _statistics = new DownloadStatistics
                {
                    TotalAlbums = selectedAlbums.Count
                };
                _stopwatch.Restart();
            }

            OnStatusChanged(DownloadStatus.Discovering);
            OnProgressUpdated("Discovering images in selected albums...");

            // Create target directory if it doesn't exist
            Directory.CreateDirectory(targetDirectory);

            // Process each album
            foreach (var album in selectedAlbums)
            {
                token.ThrowIfCancellationRequested();
                await ProcessAlbumAsync(targetDirectory, album, token);

                lock (_lockObject)
                {
                    _statistics.ProcessedAlbums++;
                }
            }

            // Mark as completed
            lock (_lockObject)
            {
                _status = DownloadStatus.Completed;
                _stopwatch.Stop();
                _statistics.ElapsedTime = _stopwatch.Elapsed;
            }

            OnStatusChanged(DownloadStatus.Completed);
            OnProgressUpdated("Download completed successfully!");

            _logger.LogInformation("Download completed successfully. Downloaded {DownloadedPhotos} photos in {ElapsedTime}",
                _statistics.DownloadedPhotos, _statistics.ElapsedTime);
        }
        catch (OperationCanceledException)
        {
            lock (_lockObject)
            {
                _status = DownloadStatus.Cancelled;
                _stopwatch.Stop();
                _statistics.ElapsedTime = _stopwatch.Elapsed;
            }

            OnStatusChanged(DownloadStatus.Cancelled);
            OnProgressUpdated("Download cancelled by user");
            _logger.LogInformation("Download cancelled by user");
        }
        catch (Exception ex)
        {
            lock (_lockObject)
            {
                _status = DownloadStatus.Error;
                _stopwatch.Stop();
                _statistics.ElapsedTime = _stopwatch.Elapsed;
            }

            OnStatusChanged(DownloadStatus.Error);
            OnProgressUpdated($"Download failed: {ex.Message}");
            OnErrorOccurred(ex, "Download operation failed");
            _logger.LogError(ex, "Download failed");
            throw;
        }
    }

    public async Task PauseAsync()
    {
        _logger.LogInformation("Pausing download");
        lock (_lockObject)
        {
            if (_status == DownloadStatus.Downloading)
            {
                _status = DownloadStatus.Paused;
                _stopwatch.Stop();
            }
        }
        OnStatusChanged(DownloadStatus.Paused);
        await Task.CompletedTask;
    }

    public async Task ResumeAsync()
    {
        _logger.LogInformation("Resuming download");
        lock (_lockObject)
        {
            if (_status == DownloadStatus.Paused)
            {
                _status = DownloadStatus.Downloading;
                _stopwatch.Start();
            }
        }
        OnStatusChanged(DownloadStatus.Downloading);
        await Task.CompletedTask;
    }

    public async Task CancelAsync()
    {
        _logger.LogInformation("Cancelling download");
        _cancellationTokenSource?.Cancel();
        await Task.CompletedTask;
    }

    public DownloadSummary GetDownloadSummary()
    {
        return new DownloadSummary
        {
            FinalStatus = _status,
            Statistics = _statistics,
            StartTime = DateTime.Now.Subtract(_statistics.ElapsedTime),
            EndTime = DateTime.Now
        };
    }

    /// <summary>
    /// Process a single album - create folder structure and download images
    /// </summary>
    private async Task ProcessAlbumAsync(string targetDirectory, SelectableAlbum album, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Processing album: {AlbumName} ({AlbumKey})", album.Name, album.AlbumKey);

            // Create album folder structure
            var albumFolderPath = CreateAlbumFolderStructure(targetDirectory, album);

            // Get images in this album
            var images = _apiClient.GetAlbumImages(album.AlbumKey, cancellationToken);

            lock (_lockObject)
            {
                _statistics.TotalPhotos += images.Count;
                _status = DownloadStatus.Downloading;
            }

            OnStatusChanged(DownloadStatus.Downloading);
            OnProgressUpdated($"Downloading {images.Count} images from album: {album.Name}");

            // Download each image
            foreach (var image in images)
            {
                cancellationToken.ThrowIfCancellationRequested();

                // Wait if paused
                while (_status == DownloadStatus.Paused)
                {
                    await Task.Delay(100, cancellationToken);
                }

                await DownloadImageAsync(albumFolderPath, image, cancellationToken);

                lock (_lockObject)
                {
                    _statistics.DownloadedPhotos++;
                    _statistics.ElapsedTime = _stopwatch.Elapsed;

                    // Calculate progress and speed
                    if (_statistics.TotalPhotos > 0)
                    {
                        var progressPercentage = (double)_statistics.DownloadedPhotos / _statistics.TotalPhotos * 100;
                        if (_statistics.ElapsedTime.TotalSeconds > 0)
                        {
                            _statistics.AverageDownloadSpeed = _statistics.DownloadedPhotos / _statistics.ElapsedTime.TotalSeconds;
                            if (_statistics.AverageDownloadSpeed > 0)
                            {
                                var remainingPhotos = _statistics.TotalPhotos - _statistics.DownloadedPhotos;
                                _statistics.EstimatedTimeRemaining = TimeSpan.FromSeconds(remainingPhotos / _statistics.AverageDownloadSpeed.Value);
                            }
                        }
                    }
                }

                OnProgressUpdated($"Downloaded {_statistics.DownloadedPhotos}/{_statistics.TotalPhotos} images", image);
            }

            _logger.LogInformation("Completed album: {AlbumName} - Downloaded {ImageCount} images",
                album.Name, images.Count);
        }
        catch (Exception ex)
        {
            lock (_lockObject)
            {
                _statistics.FailedPhotos++;
            }

            _logger.LogError(ex, "Failed to process album: {AlbumName} ({AlbumKey})", album.Name, album.AlbumKey);
            OnErrorOccurred(ex, $"Failed to process album: {album.Name}", album: null);
            // Continue with next album instead of failing completely
        }
    }

    /// <summary>
    /// Create folder structure for an album using SmugMug UrlPath to maintain folder hierarchy
    /// </summary>
    private string CreateAlbumFolderStructure(string targetDirectory, SelectableAlbum album)
    {
        string albumFolderPath;

        // Use UrlPath if available to maintain SmugMug folder structure
        if (!string.IsNullOrEmpty(album.FullPath))
        {
            _logger.LogDebug("Using UrlPath for album folder structure: {UrlPath}", album.FullPath);

            // Remove leading slash and split into path components
            var urlPath = album.FullPath.TrimStart('/');
            var pathComponents = urlPath.Split('/', StringSplitOptions.RemoveEmptyEntries);

            // Create safe folder names for each path component
            var safeFolderComponents = pathComponents.Select(GetSafeFolderName).ToArray();

            // Build the complete folder path
            albumFolderPath = targetDirectory;
            foreach (var component in safeFolderComponents)
            {
                if (!string.IsNullOrEmpty(component))
                {
                    albumFolderPath = Path.Combine(albumFolderPath, component);
                }
            }

            _logger.LogDebug("Converted UrlPath '{UrlPath}' to local path: {LocalPath}", album.FullPath, albumFolderPath);
        }
        else
        {
            // Fallback: Use album name if no UrlPath is available
            _logger.LogDebug("No UrlPath available, using album name for folder: {AlbumName}", album.Name);

            var safeFolderName = GetSafeFolderName(album.Name);
            if (string.IsNullOrEmpty(safeFolderName))
            {
                safeFolderName = $"Album_{album.AlbumKey}";
            }

            albumFolderPath = Path.Combine(targetDirectory, safeFolderName);
        }

        // Create all directories in the path (this handles nested folder creation)
        Directory.CreateDirectory(albumFolderPath);

        _logger.LogDebug("Created album folder structure: {FolderPath}", albumFolderPath);
        return albumFolderPath;
    }

    /// <summary>
    /// Download a single image
    /// </summary>
    private async Task DownloadImageAsync(string albumFolderPath, SmugMugImage image, CancellationToken cancellationToken)
    {
        try
        {
            // Get the image download URL (we'll need to implement this in the API client)
            var imageUrl = await GetImageDownloadUrlAsync(image, cancellationToken);
            if (string.IsNullOrEmpty(imageUrl))
            {
                _logger.LogWarning("No download URL found for image: {ImageKey}", image.ImageKey);
                return;
            }

            // Create safe file name
            var safeFileName = image.SafeFileName;
            var filePath = Path.Combine(albumFolderPath, safeFileName);

            // Handle duplicate file names
            var originalPath = filePath;
            var counter = 1;
            while (File.Exists(filePath))
            {
                var extension = Path.GetExtension(originalPath);
                var nameWithoutExtension = Path.GetFileNameWithoutExtension(originalPath);
                filePath = Path.Combine(albumFolderPath, $"{nameWithoutExtension}_{counter}{extension}");
                counter++;
            }

            // Download the image
            using var imageStream = await _apiClient.DownloadImageAsync(imageUrl, null, cancellationToken);
            using var fileStream = File.Create(filePath);
            await imageStream.CopyToAsync(fileStream, cancellationToken);

            _logger.LogDebug("Downloaded image: {FileName} to {FilePath}", image.SafeFileName, filePath);
        }
        catch (Exception ex)
        {
            lock (_lockObject)
            {
                _statistics.FailedPhotos++;
            }

            _logger.LogError(ex, "Failed to download image: {ImageKey} ({FileName})", image.ImageKey, image.SafeFileName);
            OnErrorOccurred(ex, $"Failed to download image: {image.SafeFileName}", image);
        }
    }

    /// <summary>
    /// Get download URL for an image using SmugMug API
    /// </summary>
    private async Task<string?> GetImageDownloadUrlAsync(SmugMugImage image, CancellationToken cancellationToken)
    {
        // First, try to use the ArchivedUri if available (this is the preferred method)
        if (!string.IsNullOrEmpty(image.ArchivedUri))
        {
            _logger.LogDebug("Using ArchivedUri for image {ImageKey}: {Url} (Size: {Size} bytes)",
                image.ImageKey, image.ArchivedUri, image.ArchivedSize ?? 0);
            return image.ArchivedUri;
        }

        _logger.LogDebug("No ArchivedUri available for image {ImageKey}, trying alternative methods", image.ImageKey);

        try
        {
            // Fallback: Get the image size details from SmugMug API
            if (image.Uris?.ImageSizeDetails != null)
            {
                var imageSizes = await _apiClient.GetImageSizeDetailsAsync(image.ImageKey, cancellationToken);

                // Try to get the original size first, then largest available
                var bestSize = imageSizes.GetOriginalSize() ?? imageSizes.GetLargestAvailableSize();

                if (bestSize != null && !string.IsNullOrEmpty(bestSize.Url))
                {
                    _logger.LogDebug("Found download URL for image {ImageKey}: {Url} ({Size})",
                        image.ImageKey, bestSize.Url, bestSize.SizeDescription);
                    return bestSize.Url;
                }
            }

            _logger.LogWarning("No size details available for image: {ImageKey}, using fallback URL construction", image.ImageKey);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get image size details for: {ImageKey}, using fallback URL construction", image.ImageKey);
        }

        // Last resort: Fallback to basic URL construction - this may not work for all images
        // SmugMug's URL structure can vary, so this is a best-effort attempt
        var fallbackUrl = $"https://photos.smugmug.com/photos/{image.ImageKey}/0/O/{image.SafeFileName}";
        _logger.LogDebug("Using fallback URL for image {ImageKey}: {Url}", image.ImageKey, fallbackUrl);
        return fallbackUrl;
    }

    /// <summary>
    /// Create a safe folder name from album name
    /// </summary>
    private static string GetSafeFolderName(string albumName)
    {
        if (string.IsNullOrWhiteSpace(albumName))
            return string.Empty;

        var invalidChars = Path.GetInvalidFileNameChars();
        var safeName = albumName;

        foreach (var invalidChar in invalidChars)
        {
            safeName = safeName.Replace(invalidChar, '_');
        }

        // Also replace some additional problematic characters
        safeName = safeName.Replace(':', '_')
                          .Replace('*', '_')
                          .Replace('?', '_')
                          .Replace('"', '_')
                          .Replace('<', '_')
                          .Replace('>', '_')
                          .Replace('|', '_');

        return safeName.Trim();
    }

    /// <summary>
    /// Event helper methods
    /// </summary>
    private void OnProgressUpdated(string? currentOperation = null, SmugMugImage? currentImage = null)
    {
        ProgressUpdated?.Invoke(this, new DownloadProgressEventArgs(_statistics, currentOperation, currentImage));
    }

    private void OnStatusChanged(DownloadStatus newStatus)
    {
        StatusChanged?.Invoke(this, new DownloadStatusChangedEventArgs(_status, newStatus));
    }

    private void OnErrorOccurred(Exception exception, string? context = null, SmugMugImage? image = null, SmugMugAlbum? album = null)
    {
        ErrorOccurred?.Invoke(this, new DownloadErrorEventArgs(exception, context, image, album));
    }
}
