# Quick Setup Guide

## For End Users

**You don't need any API keys or developer accounts!** 

Simply:
1. Download and run the application
2. Click "Authenticate with SmugMug"
3. Log in with your regular SmugMug username and password
4. Enter the verification code
5. Start downloading your photos!

## For Developers/Distributors

If you're building or distributing this application, you need to:

### 1. Get SmugMug API Credentials

1. Go to https://api.smugmug.com/api/developer/apply
2. Accept the terms and conditions to get your API key
3. You'll receive:
   - **API Key** (this becomes the OAuth Consumer Key)
   - **API Secret** (this becomes the OAuth Consumer Secret)

### 2. Configure the Application

Create `src/Winmug/appsettings.local.json`:

```json
{
  "SmugMugOAuth": {
    "ConsumerKey": "your-api-key-here",
    "ConsumerSecret": "your-api-secret-here"
  }
}
```

### 3. Build and Distribute

```bash
dotnet build --configuration Release
```

The built application can be distributed to end users who will authenticate with their own SmugMug accounts.

## How Authentication Works

```
Application (with API Key) → SmugMug OAuth → User Login → Full Access → User's Private Photos
```

1. **Application**: Uses your API key for OAuth setup
2. **OAuth Request**: Requests "Full" access to user's private content
3. **User**: Logs in with their SmugMug credentials and authorizes full access
4. **Result**: User gets access to download their entire photo library (including private content)

### Access Levels

- **Public Access**: Only publicly visible photos (not sufficient for this app)
- **Full Access**: All user content including private albums (required for complete download)

## Security Notes

- API credentials are only used for OAuth setup
- Users authenticate with their own SmugMug accounts
- No user credentials are stored by the application
- Each user gets their own access token for their photos
- Access tokens are stored securely using Windows Data Protection

## Distribution Options

### Option 1: Pre-configured Application
- Include your API credentials in the build
- Users just download and run
- Simplest for end users

### Option 2: User Configuration
- Distribute without API credentials
- Users get their own API key
- More complex but gives users control

### Option 3: Windows Store
- Include API credentials in store submission
- Users download from Windows Store
- Automatic updates and security

## FAQ

**Q: Do users need their own API keys?**
A: No! Users authenticate with their regular SmugMug username and password.

**Q: Can I distribute this application?**
A: Yes! Just include your API credentials in the configuration.

**Q: Is this secure?**
A: Yes! The OAuth flow ensures users authenticate directly with SmugMug, and access tokens are stored securely.

**Q: What permissions does the app need?**
A: Read access to download photos. Users can revoke access anytime from their SmugMug account settings.

**Q: Does this work with SmugMug Pro accounts?**
A: Yes! It works with all SmugMug account types.
