#!/usr/bin/env pwsh

# WinMug Debug Log Viewer
# This script helps you view the debug logs to troubleshoot the Start Download button issue

Write-Host "WinMug Debug Log Viewer" -ForegroundColor Green
Write-Host "======================" -ForegroundColor Green
Write-Host ""

# Get the log directory
$logDirectory = Join-Path $env:LOCALAPPDATA "WinMug\Logs"
$todayLogFile = Join-Path $logDirectory "winmug-$(Get-Date -Format 'yyyy-MM-dd').log"

Write-Host "Log Directory: $logDirectory" -ForegroundColor Yellow
Write-Host "Today's Log File: $todayLogFile" -ForegroundColor Yellow
Write-Host ""

# Check if log directory exists
if (Test-Path $logDirectory) {
    Write-Host "✅ Log directory exists" -ForegroundColor Green
    
    # List all log files
    $logFiles = Get-ChildItem $logDirectory -Filter "*.log" | Sort-Object LastWriteTime -Descending
    if ($logFiles.Count -gt 0) {
        Write-Host "📁 Available log files:" -ForegroundColor Cyan
        foreach ($file in $logFiles) {
            $sizeKB = [math]::Round($file.Length / 1KB, 2)
            $fileName = $file.Name
            $modTime = $file.LastWriteTime
            Write-Host "  - $fileName ($sizeKB KB, modified: $modTime)" -ForegroundColor White
        }
        Write-Host ""
        
        # Show today's log if it exists
        if (Test-Path $todayLogFile) {
            Write-Host "📋 Today's log content (last 50 lines):" -ForegroundColor Cyan
            Write-Host "----------------------------------------" -ForegroundColor Gray
            Get-Content $todayLogFile -Tail 50 | ForEach-Object {
                if ($_ -match "ERROR") {
                    Write-Host $_ -ForegroundColor Red
                } elseif ($_ -match "WARN") {
                    Write-Host $_ -ForegroundColor Yellow
                } elseif ($_ -match "DEBUG.*StartDownload|DEBUG.*CanStartDownload|DEBUG.*Selection") {
                    Write-Host $_ -ForegroundColor Magenta
                } else {
                    Write-Host $_ -ForegroundColor White
                }
            }
            Write-Host "----------------------------------------" -ForegroundColor Gray
            Write-Host ""
            
            Write-Host "🔍 To monitor logs in real-time, run:" -ForegroundColor Cyan
            Write-Host "Get-Content '$todayLogFile' -Wait -Tail 10" -ForegroundColor White
            Write-Host ""
            
            Write-Host "📝 To view full log file:" -ForegroundColor Cyan
            Write-Host "notepad '$todayLogFile'" -ForegroundColor White
            Write-Host ""
            
            Write-Host "🔎 To search for specific issues:" -ForegroundColor Cyan
            Write-Host "Select-String 'StartDownload|CanStartDownload|Selection' '$todayLogFile'" -ForegroundColor White
            
        } else {
            Write-Host "⚠️  Today's log file doesn't exist yet. Start the WinMug application to generate logs." -ForegroundColor Yellow
        }
    } else {
        Write-Host "⚠️  No log files found. Start the WinMug application to generate logs." -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ Log directory doesn't exist yet. Start the WinMug application to create it." -ForegroundColor Red
}

Write-Host ""
Write-Host "🚀 Instructions to debug the Start Download button issue:" -ForegroundColor Green
Write-Host "1. Run the WinMug application" -ForegroundColor White
Write-Host "2. Authenticate with SmugMug" -ForegroundColor White
Write-Host "3. Load your albums using Show my albums" -ForegroundColor White
Write-Host "4. Select some albums using the checkboxes" -ForegroundColor White
Write-Host "5. Try clicking the Start download button" -ForegroundColor White
Write-Host "6. Check the logs using this script to see what happened" -ForegroundColor White
Write-Host ""
Write-Host "The logs will show detailed information about:" -ForegroundColor Cyan
Write-Host "- Whether the button click was registered" -ForegroundColor White
Write-Host "- Authentication status" -ForegroundColor White
Write-Host "- Target directory status" -ForegroundColor White
Write-Host "- Number of selected albums" -ForegroundColor White
Write-Host "- Button enabled/disabled state changes" -ForegroundColor White
Write-Host "- Any errors that occur" -ForegroundColor White
