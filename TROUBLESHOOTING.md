# Winmug Troubleshooting Guide

## Application Starts But Window Closes Immediately

### Symptoms
- Application window appears briefly then disappears
- No error messages visible
- Process shows as running in Task Manager

### Likely Causes & Solutions

#### 1. Configuration Issues
**Problem**: Missing or invalid API credentials
**Solution**: 
- Check `src/Winmug/appsettings.json`
- Ensure `ConsumerKey` and `ConsumerSecret` are set to your actual SmugMug API credentials
- Make sure they're not still set to placeholder values like "YOUR_SMUGMUG_API_KEY_HERE"

#### 2. Window Opening Off-Screen
**Problem**: Window opens outside visible screen area
**Solution**:
- Check if the application is running in Task Manager
- Try Alt+Tab to cycle through open windows
- If found, drag the window back to your main screen

#### 3. Exception During Startup
**Problem**: Unhandled exception causes immediate exit
**Solution**:
- Run from command line to see error messages: `dotnet run --project src/Winmug`
- Check Windows Event Viewer for application errors
- Look for error dialog boxes that might be hidden behind other windows

#### 4. Missing Dependencies
**Problem**: Required .NET components not installed
**Solution**:
- Ensure .NET 8.0 Runtime is installed
- Try running: `dotnet --list-runtimes` to verify

## Running the Application

### Method 1: Command Line (Recommended for troubleshooting)
```bash
cd C:\Users\<USER>\OneDrive\Desktop\Projects\Winmug
dotnet run --project src/Winmug
```

### Method 2: Batch File
Double-click `run-winmug.bat` in the project root

### Method 3: Direct Executable
Navigate to `src\Winmug\bin\Debug\net8.0-windows\` and run `Winmug.exe`

## Checking If Application Is Running

1. **Task Manager**: Look for "Winmug" or "Winmug - SmugMug Photo Downloader" process
2. **Alt+Tab**: Cycle through open windows to find the Winmug window
3. **System Tray**: Check if the application minimized to system tray

## Getting Debug Information

### Enable Verbose Logging
1. Edit `src/Winmug/appsettings.json`
2. Change logging level to "Debug":
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Debug"
    }
  }
}
```

### Run with Console Output
```bash
dotnet run --project src/Winmug --verbosity normal
```

## Common Error Messages

### "SmugMug API credentials not configured"
- Update `appsettings.json` with your actual API key and secret
- Get credentials from: https://api.smugmug.com/api/developer/apply

### "Application startup failed"
- Check that all required files are present
- Verify .NET 8.0 is installed
- Try rebuilding: `dotnet build`

### "Main window failed to load"
- Dependency injection configuration issue
- Check that all required services are registered
- Verify configuration file is valid JSON

## Testing Basic Functionality

### Test 1: Console Application
```bash
dotnet run --project src/Winmug.Console
```
Should show "All core components are working correctly!"

### Test 2: Build Test
```bash
dotnet build
```
Should complete without errors

### Test 3: Unit Tests
```bash
dotnet test
```
Should show all tests passing

## If All Else Fails

1. **Clean and Rebuild**:
   ```bash
   dotnet clean
   dotnet restore
   dotnet build
   ```

2. **Check Windows Event Logs**:
   - Open Event Viewer
   - Look in Windows Logs > Application
   - Filter for errors from "Winmug" or ".NET Runtime"

3. **Run in Safe Mode**:
   - Create a minimal test version
   - Gradually add components to isolate the issue

4. **Contact Support**:
   - Include error messages from console output
   - Include Windows version and .NET version
   - Include contents of appsettings.json (without API credentials)

## Success Indicators

When working correctly, you should see:
- ✅ Winmug window opens and stays open
- ✅ "Not authenticated" status in Authentication section
- ✅ All buttons are visible and properly enabled/disabled
- ✅ Log messages appear in the Status and Log section
- ✅ No error dialogs appear

## Next Steps After Successful Launch

1. Click "Authenticate with SmugMug"
2. Log in with your SmugMug credentials
3. Enter the verification code
4. Select target directory
5. Start downloading your photos!
