using System.Text.Json.Serialization;

namespace Winmug.Core.Models;

/// <summary>
/// Represents a SmugMug image (photo or video)
/// </summary>
public class SmugMugImage
{
    [JsonPropertyName("ImageKey")]
    public string ImageKey { get; set; } = string.Empty;

    [JsonPropertyName("Title")]
    public string? Title { get; set; }

    [JsonPropertyName("Caption")]
    public string? Caption { get; set; }

    [JsonPropertyName("Keywords")]
    public string? Keywords { get; set; }

    [JsonPropertyName("KeywordsArray")]
    public string[]? KeywordsArray { get; set; }

    [JsonPropertyName("FileName")]
    public string? FileName { get; set; }

    [JsonPropertyName("Format")]
    public string? Format { get; set; }

    [JsonPropertyName("Date")]
    public DateTime? Date { get; set; }

    [JsonPropertyName("LastUpdated")]
    public DateTime? LastUpdated { get; set; }

    /// <summary>
    /// Original date/time when the photo was taken (from EXIF data)
    /// </summary>
    [JsonPropertyName("DateTimeOriginal")]
    public DateTime? DateTimeOriginal { get; set; }

    [JsonPropertyName("WebUri")]
    public string WebUri { get; set; } = string.Empty;

    [JsonPropertyName("Uri")]
    public string Uri { get; set; } = string.Empty;

    [JsonPropertyName("Hidden")]
    public bool? Hidden { get; set; }

    [JsonPropertyName("Watermark")]
    public bool? Watermark { get; set; }

    [JsonPropertyName("Latitude")]
    public double? Latitude { get; set; }

    [JsonPropertyName("Longitude")]
    public double? Longitude { get; set; }

    [JsonPropertyName("Altitude")]
    public double? Altitude { get; set; }

    [JsonPropertyName("Uris")]
    public SmugMugImageUris? Uris { get; set; }

    /// <summary>
    /// Direct download URL for the archived (original) version of the image
    /// This is the primary URL to use for downloading images from SmugMug
    /// </summary>
    [JsonPropertyName("ArchivedUri")]
    public string? ArchivedUri { get; set; }

    /// <summary>
    /// Size of the archived image in bytes
    /// </summary>
    [JsonPropertyName("ArchivedSize")]
    public long? ArchivedSize { get; set; }

    /// <summary>
    /// MD5 hash of the archived image for verification
    /// </summary>
    [JsonPropertyName("ArchivedMD5")]
    public string? ArchivedMD5 { get; set; }

    // Helper properties
    public string DisplayName => !string.IsNullOrEmpty(Title) ? Title : FileName ?? ImageKey;
    public string SafeFileName => GetSafeFileName();

    private string GetSafeFileName()
    {
        if (!string.IsNullOrEmpty(FileName))
        {
            return SanitizeFileName(FileName);
        }

        var extension = !string.IsNullOrEmpty(Format) ? $".{Format.ToLowerInvariant()}" : ".jpg";
        return SanitizeFileName($"{ImageKey}{extension}");
    }

    private static string SanitizeFileName(string fileName)
    {
        var invalidChars = Path.GetInvalidFileNameChars();
        foreach (var invalidChar in invalidChars)
        {
            fileName = fileName.Replace(invalidChar, '_');
        }
        return fileName;
    }
}

/// <summary>
/// Contains URIs for SmugMug image-related resources
/// </summary>
public class SmugMugImageUris
{
    [JsonPropertyName("ImageAlbum")]
    public SmugMugUriInfo? ImageAlbum { get; set; }

    [JsonPropertyName("ImageMetadata")]
    public SmugMugUriInfo? ImageMetadata { get; set; }

    [JsonPropertyName("ImageSizeDetails")]
    public SmugMugUriInfo? ImageSizeDetails { get; set; }
}
