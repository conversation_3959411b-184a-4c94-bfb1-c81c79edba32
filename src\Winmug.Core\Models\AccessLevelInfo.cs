namespace Winmug.Core.Models;

/// <summary>
/// Information about the user's access level and permissions
/// </summary>
public class AccessLevelInfo
{
    /// <summary>
    /// Whether the user has private access (Full vs Public)
    /// </summary>
    public bool HasPrivateAccess { get; set; }

    /// <summary>
    /// Whether the user's root node is accessible
    /// </summary>
    public bool HasNodeAccess { get; set; }

    /// <summary>
    /// The user's display name
    /// </summary>
    public string UserName { get; set; } = string.Empty;

    /// <summary>
    /// The user's nickname
    /// </summary>
    public string UserNickName { get; set; } = string.Empty;

    /// <summary>
    /// The user's nickname (alternative property name for compatibility)
    /// </summary>
    public string UserNickname { get; set; } = string.Empty;

    /// <summary>
    /// The root node ID if accessible
    /// </summary>
    public string RootNodeId { get; set; } = string.Empty;

    /// <summary>
    /// Whether the user has basic user access
    /// </summary>
    public bool HasUserAccess { get; set; }

    /// <summary>
    /// The access level (Full, Limited, Unknown)
    /// </summary>
    public string AccessLevel { get; set; } = string.Empty;

    /// <summary>
    /// Whether the user can access private content (combines private access and node access)
    /// </summary>
    public bool CanAccessPrivateContent { get; set; }

    /// <summary>
    /// Recommendations for improving access
    /// </summary>
    public string[] Recommendations { get; set; } = Array.Empty<string>();

    /// <summary>
    /// Summary of the access status
    /// </summary>
    public string Summary => CanAccessPrivateContent 
        ? "Full access - can download all content including private albums"
        : HasPrivateAccess 
            ? "Private access granted but folder structure not accessible"
            : "Limited access - can only access public content";
}
