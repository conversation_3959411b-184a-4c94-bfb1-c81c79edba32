Step-1 retreive specific album uri & response

Uri: "/api/v2/album/gQnS7z"


{
    "Request": {
        "Version": "v2",
        "Method": "GET",
        "Uri": "/api/v2/album/gQnS7z"
    },
    "Options": {
        "MethodDetails": {
            "OPTIONS": {
                "Permissions": [
                    "Read"
                ]
            },
            "GET": {
                "Permissions": [
                    "Read"
                ]
            },
            "PATCH": {
                "Permissions": [
                    "Modify"
                ]
            },
            "DELETE": {
                "Permissions": [
                    "Modify"
                ]
            }
        },
        "Methods": [
            "OPTIONS",
            "GET",
            "PATCH",
            "DELETE"
        ],
        "ParameterDescription": {
            "Varchar": "Variable length text from MIN_CHARS to MAX_CHARS (MAX_CHARS = INFINITY meaning arbitrary length)",
            "Uri": "Absolute or relative URL, restricted to Locator(s) of the supplied URL if not null.   Up to a MAX_LENGTH in length.",
            "Boolean": "For true return type true or 1, for false type false or 0",
            "Select": "Select one option. Options should pass only the \"Value\".",
            "Text": "Variable length text from MIN_CHARS to MAX_CHARS (MAX_CHARS = INFINITY meaning arbitrary length)",
            "Integer": "Integer value in the range MIN_VALUE to MAX_VALUE, inclusive"
        },
        "Parameters": {
            "PATCH": [
                {
                    "Name": "NiceName",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Description": "Use UrlName instead",
                    "Type": "Varchar",
                    "MIN_CHARS": 1,
                    "MAX_CHARS": 60,
                    "Deprecated": "2014-11-20"
                },
                {
                    "Name": "UrlName",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Description": "A human-readable name for use in the URL",
                    "Type": "Varchar",
                    "MIN_CHARS": 1,
                    "MAX_CHARS": 60
                },
                {
                    "Name": "Title",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Description": "Use Name instead",
                    "Type": "Varchar",
                    "MIN_CHARS": 0,
                    "MAX_CHARS": 255,
                    "Deprecated": "2014-11-20"
                },
                {
                    "Name": "Name",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Varchar",
                    "MIN_CHARS": 0,
                    "MAX_CHARS": 255
                },
                {
                    "Name": "PrintmarkUri",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Uri",
                    "MAX_LENGTH": "INFINITY",
                    "Locator": [
                        "Printmark"
                    ]
                },
                {
                    "Name": "WatermarkUri",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Uri",
                    "MAX_LENGTH": "INFINITY",
                    "Locator": [
                        "Watermark"
                    ]
                },
                {
                    "Name": "ThemeUri",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Description": "Only applies to legacy accounts",
                    "Type": "Uri",
                    "MAX_LENGTH": "INFINITY",
                    "Locator": [
                        "Theme"
                    ]
                },
                {
                    "Name": "TemplateUri",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Uri",
                    "MAX_LENGTH": "INFINITY",
                    "Locator": [
                        "Template"
                    ]
                },
                {
                    "Name": "AllowDownloads",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Boolean"
                },
                {
                    "Name": "Backprinting",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Varchar",
                    "MIN_CHARS": 0,
                    "MAX_CHARS": 255
                },
                {
                    "Name": "BoutiquePackaging",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": "Inherit from User",
                    "Type": "Select",
                    "OPTIONS": [
                        "No",
                        "Yes",
                        "Inherit from User"
                    ],
                    "MIN_COUNT": 1,
                    "MAX_COUNT": 1
                },
                {
                    "Name": "CanRank",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Boolean"
                },
                {
                    "Name": "Clean",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Boolean"
                },
                {
                    "Name": "Comments",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Boolean"
                },
                {
                    "Name": "Description",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Text",
                    "MIN_CHARS": 0,
                    "MAX_CHARS": 65535
                },
                {
                    "Name": "DownloadPassword",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Varchar",
                    "MIN_CHARS": 0,
                    "MAX_CHARS": 50
                },
                {
                    "Name": "EXIF",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Boolean"
                },
                {
                    "Name": "External",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Description": "An old setting that no longer has any function",
                    "Type": "Boolean",
                    "Deprecated": "2015-09-08"
                },
                {
                    "Name": "FamilyEdit",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Boolean"
                },
                {
                    "Name": "Filenames",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Boolean"
                },
                {
                    "Name": "FriendEdit",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Boolean"
                },
                {
                    "Name": "Geography",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Boolean"
                },
                {
                    "Name": "Header",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": "Custom",
                    "Description": "Appearance",
                    "Type": "Select",
                    "OPTIONS": [
                        "Custom",
                        "SmugMug"
                    ],
                    "MIN_COUNT": 1,
                    "MAX_COUNT": 1
                },
                {
                    "Name": "HideOwner",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Boolean"
                },
                {
                    "Name": "InterceptShipping",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": "Inherit from User",
                    "Description": "Personal Delivery",
                    "Type": "Select",
                    "OPTIONS": [
                        "No",
                        "Yes",
                        "Inherit from User"
                    ],
                    "MIN_COUNT": 1,
                    "MAX_COUNT": 1
                },
                {
                    "Name": "Keywords",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Varchar",
                    "MIN_CHARS": 0,
                    "MAX_CHARS": 255
                },
                {
                    "Name": "LargestSize",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": "Original",
                    "Type": "Select",
                    "OPTIONS": [
                        "Medium",
                        "Large",
                        "XLarge",
                        "X2Large",
                        "X3Large",
                        "X4Large",
                        "X5Large",
                        "4K",
                        "5K",
                        "Original"
                    ],
                    "MIN_COUNT": 1,
                    "MAX_COUNT": 1
                },
                {
                    "Name": "PackagingBranding",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Boolean"
                },
                {
                    "Name": "Password",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Varchar",
                    "MIN_CHARS": 0,
                    "MAX_CHARS": 50
                },
                {
                    "Name": "PasswordHint",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Varchar",
                    "MIN_CHARS": 0,
                    "MAX_CHARS": 255
                },
                {
                    "Name": "Printable",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Boolean"
                },
                {
                    "Name": "Privacy",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Description": "\"Private\" is not supported on legacy accounts",
                    "Type": "Select",
                    "OPTIONS": [
                        "Public",
                        "Unlisted",
                        "Private"
                    ],
                    "MIN_COUNT": 1,
                    "MAX_COUNT": 1
                },
                {
                    "Name": "ProofDays",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": 0,
                    "Type": "Integer",
                    "MIN_VALUE": "NEGATIVE_INFINITY",
                    "MAX_VALUE": "POSITIVE_INFINITY"
                },
                {
                    "Name": "ProofDigital",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Boolean"
                },
                {
                    "Name": "Protected",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Description": "Right-Click Protection",
                    "Type": "Boolean"
                },
                {
                    "Name": "Share",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Boolean"
                },
                {
                    "Name": "Slideshow",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Boolean"
                },
                {
                    "Name": "SmugSearchable",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": "Inherit from User",
                    "Type": "Select",
                    "OPTIONS": [
                        "No",
                        "Inherit from User"
                    ],
                    "MIN_COUNT": 1,
                    "MAX_COUNT": 1
                },
                {
                    "Name": "SortDirection",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": "Ascending",
                    "Type": "Select",
                    "OPTIONS": [
                        "Ascending",
                        "Descending"
                    ],
                    "MIN_COUNT": 1,
                    "MAX_COUNT": 1
                },
                {
                    "Name": "SortMethod",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": "Date Taken",
                    "Type": "Select",
                    "OPTIONS": [
                        "Position",
                        "Caption",
                        "Filename",
                        "Date Uploaded",
                        "Date Modified",
                        "Date Taken"
                    ],
                    "MIN_COUNT": 1,
                    "MAX_COUNT": 1
                },
                {
                    "Name": "SquareThumbs",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Boolean"
                },
                {
                    "Name": "UploadKey",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Description": "Guest UploadKey",
                    "Type": "Varchar",
                    "MIN_CHARS": 0,
                    "MAX_CHARS": "INFINITY"
                },
                {
                    "Name": "Watermark",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Description": "Automatically apply watermark to uploaded images?",
                    "Type": "Boolean"
                },
                {
                    "Name": "WorldSearchable",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Boolean"
                },
                {
                    "Name": "AutoRename",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": false,
                    "Description": "Auto-rename conflicting album NiceNames?",
                    "Type": "Boolean"
                },
                {
                    "Name": "SecurityType",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Select",
                    "OPTIONS": [
                        "None",
                        "Password",
                        "GrantAccess"
                    ],
                    "MIN_COUNT": 1,
                    "MAX_COUNT": 1
                },
                {
                    "Name": "MaxPhotoDownloadSize",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": "Original",
                    "Type": "Select",
                    "OPTIONS": [
                        "Medium",
                        "Large",
                        "XLarge",
                        "X2Large",
                        "X3Large",
                        "X4Large",
                        "X5Large",
                        "4K",
                        "5K",
                        "Original"
                    ],
                    "MIN_COUNT": 1,
                    "MAX_COUNT": 1
                },
                {
                    "Name": "HighlightAlbumImageUri",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Uri",
                    "MAX_LENGTH": "INFINITY",
                    "Locator": [
                        "AlbumImage"
                    ]
                },
                {
                    "Name": "AlbumTemplateUri",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Description": "Specify default album presets by providing an AlbumTemplateUri.",
                    "Type": "Uri",
                    "MAX_LENGTH": "INFINITY",
                    "Locator": [
                        "AlbumTemplate"
                    ]
                }
            ],
            "DELETE": []
        },
        "MediaTypes": [
            "application/json",
            "application/vnd.php.serialized",
            "application/x-msgpack",
            "text/html",
            "text/csv"
        ],
        "Output": [
            {
                "Name": "NiceName",
                "Description": "Use UrlName instead",
                "Type": "Varchar",
                "MIN_CHARS": 1,
                "MAX_CHARS": 60,
                "Deprecated": "2014-11-20"
            },
            {
                "Name": "UrlName",
                "Description": "A human-readable name for use in the URL",
                "Type": "Varchar",
                "MIN_CHARS": 1,
                "MAX_CHARS": 60
            },
            {
                "Name": "Title",
                "Description": "Use Name instead",
                "Type": "Varchar",
                "MIN_CHARS": 0,
                "MAX_CHARS": 255,
                "Deprecated": "2014-11-20"
            },
            {
                "Name": "Name",
                "Type": "Varchar",
                "MIN_CHARS": 0,
                "MAX_CHARS": 255
            },
            {
                "Name": "PrintmarkUri",
                "Type": "Uri",
                "MAX_LENGTH": "INFINITY",
                "Locator": [
                    "Printmark"
                ]
            },
            {
                "Name": "WatermarkUri",
                "Type": "Uri",
                "MAX_LENGTH": "INFINITY",
                "Locator": [
                    "Watermark"
                ]
            },
            {
                "Name": "ThemeUri",
                "Description": "Only applies to legacy accounts",
                "Type": "Uri",
                "MAX_LENGTH": "INFINITY",
                "Locator": [
                    "Theme"
                ]
            },
            {
                "Name": "TemplateUri",
                "Type": "Uri",
                "MAX_LENGTH": "INFINITY",
                "Locator": [
                    "Template"
                ]
            },
            {
                "Name": "AllowDownloads",
                "Type": "Boolean"
            },
            {
                "Name": "Backprinting",
                "Type": "Varchar",
                "MIN_CHARS": 0,
                "MAX_CHARS": 255
            },
            {
                "Name": "BoutiquePackaging",
                "Type": "Select",
                "OPTIONS": [
                    "No",
                    "Yes",
                    "Inherit from User"
                ],
                "MIN_COUNT": 1,
                "MAX_COUNT": 1
            },
            {
                "Name": "CanRank",
                "Type": "Boolean"
            },
            {
                "Name": "Clean",
                "Type": "Boolean"
            },
            {
                "Name": "Comments",
                "Type": "Boolean"
            },
            {
                "Name": "Description",
                "Type": "Text",
                "MIN_CHARS": 0,
                "MAX_CHARS": 65535
            },
            {
                "Name": "DownloadPassword",
                "Type": "Varchar",
                "MIN_CHARS": 0,
                "MAX_CHARS": 50
            },
            {
                "Name": "EXIF",
                "Type": "Boolean"
            },
            {
                "Name": "External",
                "Description": "An old setting that no longer has any function",
                "Type": "Boolean",
                "Deprecated": "2015-09-08"
            },
            {
                "Name": "FamilyEdit",
                "Type": "Boolean"
            },
            {
                "Name": "Filenames",
                "Type": "Boolean"
            },
            {
                "Name": "FriendEdit",
                "Type": "Boolean"
            },
            {
                "Name": "Geography",
                "Type": "Boolean"
            },
            {
                "Name": "Header",
                "Description": "Appearance",
                "Type": "Select",
                "OPTIONS": [
                    "Custom",
                    "SmugMug"
                ],
                "MIN_COUNT": 1,
                "MAX_COUNT": 1
            },
            {
                "Name": "HideOwner",
                "Type": "Boolean"
            },
            {
                "Name": "InterceptShipping",
                "Description": "Personal Delivery",
                "Type": "Select",
                "OPTIONS": [
                    "No",
                    "Yes",
                    "Inherit from User"
                ],
                "MIN_COUNT": 1,
                "MAX_COUNT": 1
            },
            {
                "Name": "Keywords",
                "Type": "Varchar",
                "MIN_CHARS": 0,
                "MAX_CHARS": 255
            },
            {
                "Name": "LargestSize",
                "Type": "Select",
                "OPTIONS": [
                    "Medium",
                    "Large",
                    "XLarge",
                    "X2Large",
                    "X3Large",
                    "X4Large",
                    "X5Large",
                    "4K",
                    "5K",
                    "Original"
                ],
                "MIN_COUNT": 1,
                "MAX_COUNT": 1
            },
            {
                "Name": "PackagingBranding",
                "Type": "Boolean"
            },
            {
                "Name": "Password",
                "Type": "Varchar",
                "MIN_CHARS": 0,
                "MAX_CHARS": 50
            },
            {
                "Name": "PasswordHint",
                "Type": "Varchar",
                "MIN_CHARS": 0,
                "MAX_CHARS": 255
            },
            {
                "Name": "Printable",
                "Type": "Boolean"
            },
            {
                "Name": "Privacy",
                "Description": "\"Private\" is not supported on legacy accounts",
                "Type": "Select",
                "OPTIONS": [
                    "Public",
                    "Unlisted",
                    "Private"
                ],
                "MIN_COUNT": 1,
                "MAX_COUNT": 1
            },
            {
                "Name": "ProofDays",
                "Type": "Integer",
                "MIN_VALUE": "NEGATIVE_INFINITY",
                "MAX_VALUE": "POSITIVE_INFINITY"
            },
            {
                "Name": "ProofDigital",
                "Type": "Boolean"
            },
            {
                "Name": "Protected",
                "Description": "Right-Click Protection",
                "Type": "Boolean"
            },
            {
                "Name": "Share",
                "Type": "Boolean"
            },
            {
                "Name": "Slideshow",
                "Type": "Boolean"
            },
            {
                "Name": "SmugSearchable",
                "Type": "Select",
                "OPTIONS": [
                    "No",
                    "Inherit from User"
                ],
                "MIN_COUNT": 1,
                "MAX_COUNT": 1
            },
            {
                "Name": "SortDirection",
                "Type": "Select",
                "OPTIONS": [
                    "Ascending",
                    "Descending"
                ],
                "MIN_COUNT": 1,
                "MAX_COUNT": 1
            },
            {
                "Name": "SortMethod",
                "Type": "Select",
                "OPTIONS": [
                    "Position",
                    "Caption",
                    "Filename",
                    "Date Uploaded",
                    "Date Modified",
                    "Date Taken"
                ],
                "MIN_COUNT": 1,
                "MAX_COUNT": 1
            },
            {
                "Name": "SquareThumbs",
                "Type": "Boolean"
            },
            {
                "Name": "UploadKey",
                "Description": "Guest UploadKey",
                "Type": "Varchar",
                "MIN_CHARS": 0,
                "MAX_CHARS": "INFINITY"
            },
            {
                "Name": "Watermark",
                "Description": "Automatically apply watermark to uploaded images?",
                "Type": "Boolean"
            },
            {
                "Name": "WorldSearchable",
                "Type": "Boolean"
            },
            {
                "Name": "SecurityType",
                "Type": "Select",
                "OPTIONS": [
                    "None",
                    "Password",
                    "GrantAccess"
                ],
                "MIN_COUNT": 1,
                "MAX_COUNT": 1
            },
            {
                "Name": "CommerceLightbox",
                "Type": "Boolean"
            },
            {
                "Name": "MaxPhotoDownloadSize",
                "Type": "Select",
                "OPTIONS": [
                    "Medium",
                    "Large",
                    "XLarge",
                    "X2Large",
                    "X3Large",
                    "X4Large",
                    "X5Large",
                    "4K",
                    "5K",
                    "Original"
                ],
                "MIN_COUNT": 1,
                "MAX_COUNT": 1
            },
            {
                "Name": "HighlightAlbumImageUri",
                "Type": "Uri",
                "MAX_LENGTH": "INFINITY",
                "Locator": [
                    "AlbumImage"
                ]
            },
            {
                "Name": "AlbumTemplateUri",
                "Description": "Specify default album presets by providing an AlbumTemplateUri.",
                "Type": "Uri",
                "MAX_LENGTH": "INFINITY",
                "Locator": [
                    "AlbumTemplate"
                ]
            },
            {
                "Name": "AlbumKey",
                "Type": "Varchar",
                "MIN_CHARS": 0,
                "MAX_CHARS": "INFINITY"
            },
            {
                "Name": "CanBuy",
                "Type": "Boolean"
            },
            {
                "Name": "CanFavorite",
                "Type": "Boolean"
            },
            {
                "Name": "FavoriteAlbumUrl",
                "Type": "FullUrl",
                "MIN_CHARS": 0,
                "MAX_CHARS": "INFINITY"
            },
            {
                "Name": "Date",
                "Type": "DateTimeISO8601"
            },
            {
                "Name": "LastUpdated",
                "Type": "DateTimeISO8601"
            },
            {
                "Name": "ImagesLastUpdated",
                "Description": "The last time at which images were added, removed, or rearranged in this album",
                "Type": "DateTimeISO8601"
            },
            {
                "Name": "NodeID",
                "Type": "Varchar",
                "MIN_CHARS": 1,
                "MAX_CHARS": "INFINITY"
            },
            {
                "Name": "OriginalSizes",
                "Type": "Integer",
                "MIN_VALUE": "NEGATIVE_INFINITY",
                "MAX_VALUE": "POSITIVE_INFINITY"
            },
            {
                "Name": "TotalSizes",
                "Type": "Integer",
                "MIN_VALUE": "NEGATIVE_INFINITY",
                "MAX_VALUE": "POSITIVE_INFINITY"
            },
            {
                "Name": "ImageCount",
                "Type": "Integer",
                "MIN_VALUE": "NEGATIVE_INFINITY",
                "MAX_VALUE": "POSITIVE_INFINITY"
            },
            {
                "Name": "UrlPath",
                "Type": "Varchar",
                "MIN_CHARS": 0,
                "MAX_CHARS": "INFINITY"
            },
            {
                "Name": "CanShare",
                "Description": "Does the owner of this album want it to be shareable?",
                "Type": "Boolean"
            },
            {
                "Name": "HasDownloadPassword",
                "Type": "Boolean"
            },
            {
                "Name": "Packages",
                "Description": "Are any packages available for album",
                "Type": "Boolean"
            }
        ],
        "Notes": [
            "To sort images manually first set SortMethod to Position",
            "When selecting SortMethod=Position, SortMethod is automatically set to Ascending",
            "NiceName is deprecated; use UrlName instead",
            "Title is deprecated; use Name instead"
        ],
        "ResponseLevels": [
            "Full",
            "Password",
            "Public",
            "GrantAccess"
        ],
        "Path": [
            {
                "type": "path",
                "text": "api"
            },
            {
                "type": "path",
                "text": "v2"
            },
            {
                "type": "path",
                "text": "album"
            },
            {
                "type": "singleparam",
                "param_name": "albumkey",
                "param_value": "gQnS7z",
                "param_validator": {}
            }
        ]
    },
    "Response": {
        "Uri": "/api/v2/album/gQnS7z",
        "Locator": "Album",
        "LocatorType": "Object",
        "Album": {
            "NiceName": "TestGallery",
            "UrlName": "TestGallery",
            "Title": "TestGallery",
            "Name": "TestGallery",
            "TemplateUri": "/api/v2/template/18",
            "AllowDownloads": false,
            "Backprinting": "",
            "BoutiquePackaging": "No",
            "CanRank": true,
            "Clean": false,
            "Comments": true,
            "Description": "test-gallery",
            "EXIF": true,
            "External": true,
            "FamilyEdit": false,
            "Filenames": false,
            "FriendEdit": false,
            "Geography": true,
            "Header": "Custom",
            "HideOwner": false,
            "InterceptShipping": "Inherit from User",
            "Keywords": "",
            "LargestSize": "X4Large",
            "PackagingBranding": true,
            "Password": "**********",
            "PasswordHint": "",
            "Printable": true,
            "Privacy": "Public",
            "ProofDays": 0,
            "ProofDigital": false,
            "Protected": false,
            "Share": true,
            "Slideshow": true,
            "SmugSearchable": "Inherit from User",
            "SortDirection": "Ascending",
            "SortMethod": "Position",
            "SquareThumbs": true,
            "Watermark": false,
            "WorldSearchable": true,
            "SecurityType": "Password",
            "MaxPhotoDownloadSize": "Original",
            "HighlightAlbumImageUri": "/api/v2/album/gQnS7z/image/GNdg5g2-0",
            "AlbumTemplateUri": "/api/v2/albumtemplate/562612",
            "AlbumKey": "gQnS7z",
            "CanBuy": true,
            "CanFavorite": false,
            "Date": "2025-06-20T18:42:23+00:00",
            "LastUpdated": "2025-06-20T18:42:23+00:00",
            "ImagesLastUpdated": "2025-06-20T18:43:05+00:00",
            "NodeID": "G2qFM5",
            "OriginalSizes": 0,
            "TotalSizes": 0,
            "ImageCount": 7,
            "UrlPath": "/TestGallery",
            "CanShare": true,
            "HasDownloadPassword": false,
            "Packages": false,
            "Uri": "/api/v2/album/gQnS7z",
            "WebUri": "https://yuvin.smugmug.com/TestGallery",
            "UriDescription": "Album by key",
            "Uris": {
                "AlbumShareUris": {
                    "Uri": "/api/v2/album/gQnS7z!shareuris",
                    "Locator": "AlbumShareUris",
                    "LocatorType": "Object",
                    "UriDescription": "URIs that are useful for sharing",
                    "EndpointType": "AlbumShareUris"
                },
                "Node": {
                    "Uri": "/api/v2/node/G2qFM5",
                    "Locator": "Node",
                    "LocatorType": "Object",
                    "UriDescription": "Node with the given id.",
                    "EndpointType": "Node"
                },
                "NodeCoverImage": {
                    "Uri": "/api/v2/node/G2qFM5!cover",
                    "Locator": "Image",
                    "LocatorType": "Object",
                    "UriDescription": "Cover image for a folder, album, or page",
                    "EndpointType": "NodeCoverImage"
                },
                "User": {
                    "Uri": "/api/v2/user/yuvin",
                    "Locator": "User",
                    "LocatorType": "Object",
                    "UriDescription": "User By Nickname",
                    "EndpointType": "User"
                },
                "Folder": {
                    "Uri": "/api/v2/folder/user/yuvin",
                    "Locator": "Folder",
                    "LocatorType": "Object",
                    "UriDescription": "A folder or legacy (sub)category by UrlPath",
                    "EndpointType": "Folder"
                },
                "ParentFolders": {
                    "Uri": "/api/v2/folder/user/yuvin!parents",
                    "Locator": "Folder",
                    "LocatorType": "Objects",
                    "UriDescription": "The sequence of parent folders, from the given folder to the root",
                    "EndpointType": "ParentFolders"
                },
                "HighlightImage": {
                    "Uri": "/api/v2/highlight/node/G2qFM5",
                    "Locator": "Image",
                    "LocatorType": "Object",
                    "UriDescription": "Highlight image for a folder, album, or page",
                    "EndpointType": "HighlightImage"
                },
                "AddSamplePhotos": {
                    "Uri": "/api/v2/album/gQnS7z!addsamplephotos",
                    "UriDescription": "Add sample photos to Album",
                    "EndpointType": "AddSamplePhotos"
                },
                "AlbumHighlightImage": {
                    "Uri": "/api/v2/album/gQnS7z!highlightimage",
                    "Locator": "AlbumImage",
                    "LocatorType": "Object",
                    "UriDescription": "Highlight image for album",
                    "EndpointType": "AlbumHighlightImage"
                },
                "AlbumImages": {
                    "Uri": "/api/v2/album/gQnS7z!images",
                    "Locator": "AlbumImage",
                    "LocatorType": "Objects",
                    "UriDescription": "Images from album",
                    "EndpointType": "AlbumImages"
                },
                "AlbumPopularMedia": {
                    "Uri": "/api/v2/album/gQnS7z!popularmedia",
                    "Locator": "AlbumImage",
                    "LocatorType": "Objects",
                    "UriDescription": "Popular images from album",
                    "EndpointType": "AlbumPopularMedia"
                },
                "AlbumPackages": {
                    "Uri": "/api/v2/album/gQnS7z!packages",
                    "Locator": "Package",
                    "LocatorType": "Objects",
                    "UriDescription": "Album packages",
                    "EndpointType": "AlbumPackages"
                },
                "AlbumGeoMedia": {
                    "Uri": "/api/v2/album/gQnS7z!geomedia",
                    "Locator": "AlbumImage",
                    "LocatorType": "Objects",
                    "UriDescription": "Geotagged images from album",
                    "EndpointType": "AlbumGeoMedia"
                },
                "AlbumComments": {
                    "Uri": "/api/v2/album/gQnS7z!comments",
                    "Locator": "Comment",
                    "LocatorType": "Objects",
                    "UriDescription": "Comments on album",
                    "EndpointType": "AlbumComments"
                },
                "SortAlbumImages": {
                    "Uri": "/api/v2/album/gQnS7z!sortimages",
                    "UriDescription": "Change the ordering of images in a manually-ordered album",
                    "EndpointType": "SortAlbumImages"
                },
                "MoveAlbumImages": {
                    "Uri": "/api/v2/album/gQnS7z!moveimages",
                    "UriDescription": "Move images into album",
                    "EndpointType": "MoveAlbumImages"
                },
                "CollectImages": {
                    "Uri": "/api/v2/album/gQnS7z!collectimages",
                    "UriDescription": "Collect images into album",
                    "EndpointType": "CollectImages"
                },
                "ApplyAlbumTemplate": {
                    "Uri": "/api/v2/album/gQnS7z!applyalbumtemplate",
                    "Locator": "ApplyAlbumTemplate",
                    "LocatorType": "Object",
                    "UriDescription": "Apply an album template",
                    "EndpointType": "ApplyAlbumTemplate"
                },
                "DeleteAlbumImages": {
                    "Uri": "/api/v2/album/gQnS7z!deleteimages",
                    "UriDescription": "Delete album images",
                    "EndpointType": "DeleteAlbumImages"
                },
                "AlbumTemplate": {
                    "Uri": "/api/v2/albumtemplate/562612",
                    "Locator": "AlbumTemplate",
                    "LocatorType": "Object",
                    "UriDescription": "Album Template by ID",
                    "EndpointType": "AlbumTemplate"
                },
                "UploadFromExternalResource": {
                    "Uri": "/api/v2/album/gQnS7z!uploadfromexternalresource",
                    "UriDescription": "Upload a photo or video from external storage",
                    "EndpointType": "UploadFromExternalResource"
                },
                "UploadFromUri": {
                    "Uri": "/api/v2/album/gQnS7z!uploadfromuri",
                    "UriDescription": "Upload a photo or video from elsewhere on the web",
                    "EndpointType": "UploadFromUri"
                },
                "AlbumGrants": {
                    "Uri": "/api/v2/album/gQnS7z!grants",
                    "Locator": "Grant",
                    "LocatorType": "Objects",
                    "UriDescription": "Grants for the Album",
                    "EndpointType": "AlbumGrants"
                },
                "AlbumDownload": {
                    "Uri": "/api/v2/album/gQnS7z!download",
                    "Locator": "Download",
                    "LocatorType": "Objects",
                    "UriDescription": "Download album",
                    "EndpointType": "AlbumDownload"
                },
                "AlbumPrices": {
                    "Uri": "/api/v2/album/gQnS7z!prices",
                    "Locator": "CatalogSkuPrice",
                    "LocatorType": "Objects",
                    "UriDescription": "Purchasable Skus",
                    "EndpointType": "AlbumPrices"
                },
                "AlbumPricelistExclusions": {
                    "Uri": "/api/v2/album/gQnS7z!pricelistexclusions",
                    "Locator": "AlbumPricelistExclusions",
                    "LocatorType": "Object",
                    "UriDescription": "Pricelist information for an Album",
                    "EndpointType": "AlbumPricelistExclusions"
                }
            },
            "ResponseLevel": "Full"
        },
        "UriDescription": "Album by key",
        "EndpointType": "Album",
        "DocUri": "https://api.smugmug.com/api/v2/doc/reference/album.html",
        "Timing": {
            "Total": {
                "time": 0.05043,
                "cycles": 1,
                "objects": 0
            }
        }
    },
    "Code": 200,
    "Message": "Ok"
}

Step-2 Use ArchivedUri
to download the image

{
    "Request": {
        "Version": "v2",
        "Method": "GET",
        "Uri": "/api/v2/album/4qL53X/image/6fNSrGB-0"
    },
    "Options": {
        "MethodDetails": {
            "OPTIONS": {
                "Permissions": [
                    "Read"
                ]
            },
            "GET": {
                "Permissions": [
                    "Read"
                ]
            },
            "PATCH": {
                "Permissions": [
                    "Modify"
                ]
            },
            "DELETE": {
                "Permissions": [
                    "Modify"
                ]
            }
        },
        "Methods": [
            "OPTIONS",
            "GET",
            "PATCH",
            "DELETE"
        ],
        "ParameterDescription": {
            "Varchar": "Variable length text from MIN_CHARS to MAX_CHARS (MAX_CHARS = INFINITY meaning arbitrary length)",
            "Array": "An indexed array of values with a length in the range MIN_COUNT to MAX_COUNT, inclusive.",
            "Select": "Select one option. Options should pass only the \"Value\".",
            "DECIMAL": "Decimal character of PRECISION digits with SCALE digits to the right of the decimal point.  SIGNED indicates if the decimal number can be negative (true) or only positive (false).  Passed as strings.",
            "Integer": "Integer value in the range MIN_VALUE to MAX_VALUE, inclusive",
            "Boolean": "For true return type true or 1, for false type false or 0"
        },
        "Parameters": {
            "PATCH": [
                {
                    "Name": "Title",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": "",
                    "Type": "Varchar",
                    "MIN_CHARS": 0,
                    "MAX_CHARS": "INFINITY"
                },
                {
                    "Name": "Caption",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Varchar",
                    "MIN_CHARS": 0,
                    "MAX_CHARS": "INFINITY"
                },
                {
                    "Name": "Keywords",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Varchar",
                    "MIN_CHARS": 0,
                    "MAX_CHARS": "INFINITY"
                },
                {
                    "Name": "KeywordArray",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Array",
                    "ITEM_TYPE": "Varchar",
                    "MIN_COUNT": 0,
                    "MAX_COUNT": "INFINITY"
                },
                {
                    "Name": "Watermark",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": "No",
                    "Type": "Select",
                    "OPTIONS": [
                        "No",
                        "Yes",
                        "Inherit from Album"
                    ],
                    "MIN_COUNT": 1,
                    "MAX_COUNT": 1,
                    "Deprecated": "2014-11-20"
                },
                {
                    "Name": "Latitude",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": "0",
                    "Type": "DECIMAL",
                    "PRECISION": 16,
                    "SCALE": 14,
                    "SIGNED": true
                },
                {
                    "Name": "Longitude",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": "0",
                    "Type": "DECIMAL",
                    "PRECISION": 17,
                    "SCALE": 14,
                    "SIGNED": true
                },
                {
                    "Name": "Altitude",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": 0,
                    "Type": "Integer",
                    "MIN_VALUE": 0,
                    "MAX_VALUE": 16777215
                },
                {
                    "Name": "Hidden",
                    "Required": false,
                    "ReadOnly": false,
                    "Default": null,
                    "Type": "Boolean"
                }
            ],
            "DELETE": []
        },
        "MediaTypes": [
            "application/json",
            "application/vnd.php.serialized",
            "application/x-msgpack",
            "text/html",
            "text/csv"
        ],
        "Path": [
            {
                "type": "path",
                "text": "api"
            },
            {
                "type": "path",
                "text": "v2"
            },
            {
                "type": "path",
                "text": "album"
            },
            {
                "type": "singleparam",
                "param_name": "albumkey",
                "param_value": "4qL53X",
                "param_validator": {}
            },
            {
                "type": "path",
                "text": "image"
            },
            {
                "type": "singleparam",
                "param_name": "imagekey",
                "param_value": "6fNSrGB-0",
                "param_validator": {}
            }
        ]
    },
    "Response": {
        "Uri": "/api/v2/album/4qL53X/image/6fNSrGB-0",
        "Locator": "AlbumImage",
        "LocatorType": "Object",
        "AlbumImage": {
            "Title": "",
            "Caption": "",
            "Keywords": "",
            "KeywordArray": [],
            "Watermark": "No",
            "Latitude": "0",
            "Longitude": "0",
            "Altitude": 0,
            "Hidden": false,
            "ThumbnailUrl": "https://photos.smugmug.com/photos/i-6fNSrGB/0/MWsCSH5Vvg4z56DXRrmVN5RvqHr8RFHzxmLQVNK2w/Th/i-6fNSrGB-Th.jpg",
            "FileName": "7862a0fa-a1f0-49aa-a1f1-023cb8e79e49.jpg",
            "Processing": false,
            "UploadKey": "16898974988",
            "Date": "2025-06-13T18:48:46+00:00",
            "DateTimeUploaded": "2025-06-13T18:48:46+00:00",
            "Format": "JPG",
            "OriginalHeight": 1424,
            "OriginalWidth": 1110,
            "OriginalSize": 138501,
            "LastUpdated": "2025-06-13T18:48:46+00:00",
            "Collectable": true,
            "IsArchive": false,
            "IsVideo": false,
            "ComponentFileTypes": {
                "Image": [
                    "jpg"
                ]
            },
            "CanEdit": true,
            "CanBuy": true,
            "Protected": true,
            "EZProject": false,
            "Watermarked": false,
            "ImageKey": "6fNSrGB",
            "Serial": 0,
            "ArchivedUri": "https://photos.smugmug.com/photos/i-6fNSrGB/0/M5NZWmfP7d3XnBbpcwTKxMkpC4CM83QnFqN2D9wNP/D/i-6fNSrGB-D.jpg",
            "ArchivedSize": 138501,
            "ArchivedMD5": "0f907d5f430c3660f8f9578df49cdbf6",
            "Status": "Open",
            "SubStatus": "NFS",
            "CanShare": true,
            "Comments": true,
            "ShowKeywords": true,
            "FormattedValues": {
                "Caption": {
                    "html": "",
                    "text": ""
                },
                "FileName": {
                    "html": "7862a0fa-a1f0-49aa-a1f1-023cb8e79e49.jpg",
                    "text": "7862a0fa-a1f0-49aa-a1f1-023cb8e79e49.jpg"
                }
            },
            "PreferredDisplayFileExtension": "JPG",
            "Uri": "/api/v2/album/4qL53X/image/6fNSrGB-0",
            "WebUri": "https://yuvin.smugmug.com/Automatic-iOS-Uploads/2025/06/i-6fNSrGB",
            "UriDescription": "Image from album",
            "Uris": {
                "Components": {
                    "Uri": "/api/v2/library/asset/6fNSrGB!components",
                    "Locator": "Component",
                    "LocatorType": "Objects",
                    "UriDescription": "Components available for an asset",
                    "EndpointType": "Components"
                },
                "LargestImage": {
                    "Uri": "/api/v2/image/6fNSrGB-0!largestimage",
                    "Locator": "LargestImage",
                    "LocatorType": "Object",
                    "UriDescription": "Largest size available for image",
                    "EndpointType": "LargestImage"
                },
                "ImageSizes": {
                    "Uri": "/api/v2/image/6fNSrGB-0!sizes",
                    "Locator": "ImageSizes",
                    "LocatorType": "Object",
                    "UriDescription": "Sizes available for image",
                    "EndpointType": "ImageSizes"
                },
                "ImageSizeDetails": {
                    "Uri": "/api/v2/image/6fNSrGB-0!sizedetails",
                    "Locator": "ImageSizeDetails",
                    "LocatorType": "Object",
                    "UriDescription": "Detailed size information for image",
                    "EndpointType": "ImageSizeDetails"
                },
                "PointOfInterest": {
                    "Uri": "/api/v2/image/6fNSrGB-0!pointofinterest",
                    "Locator": "PointOfInterest",
                    "LocatorType": "Object",
                    "UriDescription": "Point of interest for image",
                    "EndpointType": "PointOfInterest"
                },
                "PointOfInterestCrops": {
                    "Uri": "/api/v2/image/6fNSrGB-0!poicrops",
                    "Locator": "PointOfInterestCrops",
                    "LocatorType": "List",
                    "UriDescription": "PointOfInterest Crops for image",
                    "EndpointType": "PointOfInterestCrops"
                },
                "Regions": {
                    "Uri": "/api/v2/image/6fNSrGB-0!regions",
                    "Locator": "Region",
                    "LocatorType": "Objects",
                    "UriDescription": "Regions for image",
                    "EndpointType": "Regions"
                },
                "ImageAlbum": {
                    "Uri": "/api/v2/album/4qL53X",
                    "Locator": "Album",
                    "LocatorType": "Object",
                    "UriDescription": "Album by key",
                    "EndpointType": "Album"
                },
                "ImageOwner": {
                    "Uri": "/api/v2/user/yuvin",
                    "Locator": "User",
                    "LocatorType": "Object",
                    "UriDescription": "User By Nickname",
                    "EndpointType": "User"
                },
                "ImageAlbums": {
                    "Uri": "/api/v2/image/6fNSrGB-0!albums",
                    "Locator": "Album",
                    "LocatorType": "Objects",
                    "UriDescription": "Albums the image is included in",
                    "EndpointType": "ImageAlbums"
                },
                "ImageDownload": {
                    "Uri": "/api/v2/image/6fNSrGB-0!download",
                    "Locator": "ImageDownload",
                    "LocatorType": "Object",
                    "UriDescription": "Download image",
                    "EndpointType": "ImageDownload"
                },
                "ImageComments": {
                    "Uri": "/api/v2/image/6fNSrGB-0!comments",
                    "Locator": "Comment",
                    "LocatorType": "Objects",
                    "UriDescription": "Comments on image",
                    "EndpointType": "ImageComments"
                },
                "RotateImage": {
                    "Uri": "/api/v2/image/6fNSrGB-0!rotate",
                    "UriDescription": "Rotate an image",
                    "EndpointType": "RotateImage"
                },
                "ColorImage": {
                    "Uri": "/api/v2/image/6fNSrGB-0!color",
                    "Locator": "ColorImage",
                    "LocatorType": "Object",
                    "UriDescription": "Color an image",
                    "EndpointType": "ColorImage"
                },
                "CopyImage": {
                    "Uri": "/api/v2/image/6fNSrGB-0!copy",
                    "UriDescription": "Copy an image",
                    "EndpointType": "CopyImage"
                },
                "CropImage": {
                    "Uri": "/api/v2/image/6fNSrGB-0!crop",
                    "UriDescription": "Crop an image",
                    "EndpointType": "CropImage"
                },
                "ImageMetadata": {
                    "Uri": "/api/v2/image/6fNSrGB-0!metadata",
                    "Locator": "ImageMetadata",
                    "LocatorType": "Object",
                    "UriDescription": "Metadata for image",
                    "EndpointType": "ImageMetadata"
                },
                "ImagePrices": {
                    "Uri": "/api/v2/image/6fNSrGB-0!prices",
                    "Locator": "CatalogSkuPrice",
                    "LocatorType": "Objects",
                    "UriDescription": "Purchasable Skus",
                    "EndpointType": "ImagePrices"
                },
                "ImagePricelistExclusions": {
                    "Uri": "/api/v2/image/6fNSrGB-0!pricelistexclusions",
                    "Locator": "ImagePricelistExclusions",
                    "LocatorType": "Object",
                    "UriDescription": "Pricelist information for an image",
                    "EndpointType": "ImagePricelistExclusions"
                },
                "Album": {
                    "Uri": "/api/v2/album/4qL53X",
                    "Locator": "Album",
                    "LocatorType": "Object",
                    "UriDescription": "Album by key",
                    "EndpointType": "Album"
                },
                "Image": {
                    "Uri": "/api/v2/image/6fNSrGB-0",
                    "Locator": "Image",
                    "LocatorType": "Object",
                    "UriDescription": "Image by key",
                    "EndpointType": "Image"
                },
                "AlbumImagePricelistExclusions": {
                    "Uri": "/api/v2/album/4qL53X/image/6fNSrGB-0!pricelistexclusions",
                    "Locator": "AlbumImagePricelistExclusions",
                    "LocatorType": "Object",
                    "UriDescription": "Pricelist information for an album image",
                    "EndpointType": "AlbumImagePricelistExclusions"
                },
                "AlbumImageMetadata": {
                    "Uri": "/api/v2/album/4qL53X/image/6fNSrGB-0!metadata",
                    "Locator": "AlbumImageMetadata",
                    "LocatorType": "Object",
                    "UriDescription": "Metadata for AlbumImage",
                    "EndpointType": "AlbumImageMetadata"
                },
                "AlbumImageShareUris": {
                    "Uri": "/api/v2/album/4qL53X/image/6fNSrGB-0!shareuris",
                    "Locator": "AlbumImageShareUris",
                    "LocatorType": "Object",
                    "UriDescription": "URIs that are useful for sharing",
                    "EndpointType": "AlbumImageShareUris"
                },
                "AlbumImageDownload": {
                    "Uri": "/api/v2/album/4qL53X/image/6fNSrGB-0!download",
                    "Locator": "AlbumImageDownload",
                    "LocatorType": "Object",
                    "UriDescription": "Download the image from the album",
                    "EndpointType": "AlbumImageDownload"
                }
            },
            "AlbumKey": "4qL53X",
            "Movable": true,
            "Origin": "Album"
        },
        "UriDescription": "Image from album",
        "EndpointType": "AlbumImage",
        "DocUri": "https://api.smugmug.com/api/v2/doc/reference/album-image.html",
        "Timing": {
            "Total": {
                "time": 0.07499,
                "cycles": 1,
                "objects": 0
            }
        }
    },
    "Code": 200,
    "Message": "Ok"
}