using System.Security.Cryptography;
using System.Text;

namespace Winmug.Core.Authentication;

/// <summary>
/// Fixed OAuth 1.0a signature generator that strictly follows RFC 5849
/// This implementation is based on the official SmugMug OAuth documentation
/// </summary>
public static class OAuthSignatureGeneratorFixed
{
    /// <summary>
    /// Generates an OAuth 1.0a signature for the given request
    /// </summary>
    public static string GenerateSignature(
        string httpMethod,
        string url,
        Dictionary<string, string> parameters,
        string consumerSecret,
        string? tokenSecret = null)
    {
        // Step 1: Normalize the URL
        var normalizedUrl = NormalizeUrl(url);
        
        // Step 2: Normalize parameters
        var normalizedParameters = NormalizeParameters(parameters);
        
        // Step 3: Create signature base string
        var signatureBaseString = CreateSignatureBaseString(httpMethod, normalizedUrl, normalizedParameters);
        
        // Step 4: Create signing key
        var signingKey = CreateSigningKey(consumerSecret, tokenSecret);
        
        // Step 5: Generate HMAC-SHA1 signature
        var signature = GenerateHmacSha1Signature(signatureBaseString, signingKey);
        
        // Debug output
        System.Diagnostics.Debug.WriteLine($"[OAuth Fixed] Method: {httpMethod}");
        System.Diagnostics.Debug.WriteLine($"[OAuth Fixed] Original URL: {url}");
        System.Diagnostics.Debug.WriteLine($"[OAuth Fixed] Normalized URL: {normalizedUrl}");
        System.Diagnostics.Debug.WriteLine($"[OAuth Fixed] Normalized Parameters: {normalizedParameters}");
        System.Diagnostics.Debug.WriteLine($"[OAuth Fixed] Signature Base String: {signatureBaseString}");
        System.Diagnostics.Debug.WriteLine($"[OAuth Fixed] Signing Key: {signingKey.Substring(0, Math.Min(20, signingKey.Length))}...");
        System.Diagnostics.Debug.WriteLine($"[OAuth Fixed] Generated Signature: {signature}");
        
        return signature;
    }

    /// <summary>
    /// Normalizes the URL according to RFC 5849 Section *******
    /// </summary>
    private static string NormalizeUrl(string url)
    {
        var uri = new Uri(url);
        
        // Convert scheme and host to lowercase
        var scheme = uri.Scheme.ToLowerInvariant();
        var host = uri.Host.ToLowerInvariant();
        
        // Build normalized URL
        var normalizedUrl = $"{scheme}://{host}";
        
        // Include port only if it's not the default port
        if ((scheme == "http" && uri.Port != 80) || 
            (scheme == "https" && uri.Port != 443))
        {
            normalizedUrl += $":{uri.Port}";
        }
        
        // Add path (ensure it starts with /)
        var path = uri.AbsolutePath;
        if (string.IsNullOrEmpty(path))
        {
            path = "/";
        }
        normalizedUrl += path;
        
        return normalizedUrl;
    }

    /// <summary>
    /// Normalizes parameters according to RFC 5849 Section *******
    /// </summary>
    private static string NormalizeParameters(Dictionary<string, string> parameters)
    {
        // Sort parameters by key, then by value
        var sortedParams = parameters
            .OrderBy(kvp => kvp.Key, StringComparer.Ordinal)
            .ThenBy(kvp => kvp.Value, StringComparer.Ordinal)
            .Select(kvp => $"{PercentEncode(kvp.Key)}={PercentEncode(kvp.Value)}")
            .ToArray();
        
        return string.Join("&", sortedParams);
    }

    /// <summary>
    /// Creates the signature base string according to RFC 5849 Section 3.4.1.1
    /// </summary>
    private static string CreateSignatureBaseString(string httpMethod, string normalizedUrl, string normalizedParameters)
    {
        return $"{httpMethod.ToUpperInvariant()}&{PercentEncode(normalizedUrl)}&{PercentEncode(normalizedParameters)}";
    }

    /// <summary>
    /// Creates the signing key according to RFC 5849 Section 3.4.2
    /// </summary>
    private static string CreateSigningKey(string consumerSecret, string? tokenSecret)
    {
        return $"{PercentEncode(consumerSecret)}&{PercentEncode(tokenSecret ?? string.Empty)}";
    }

    /// <summary>
    /// Generates HMAC-SHA1 signature
    /// </summary>
    private static string GenerateHmacSha1Signature(string signatureBaseString, string signingKey)
    {
        var keyBytes = Encoding.UTF8.GetBytes(signingKey);
        var dataBytes = Encoding.UTF8.GetBytes(signatureBaseString);
        
        using var hmac = new HMACSHA1(keyBytes);
        var hashBytes = hmac.ComputeHash(dataBytes);
        return Convert.ToBase64String(hashBytes);
    }

    /// <summary>
    /// Percent encodes a string according to RFC 3986
    /// This is the exact encoding required by OAuth 1.0a
    /// </summary>
    public static string PercentEncode(string value)
    {
        if (string.IsNullOrEmpty(value))
            return string.Empty;

        var result = new StringBuilder();
        
        foreach (char c in value)
        {
            // Unreserved characters (RFC 3986 Section 2.3)
            if ((c >= 'A' && c <= 'Z') || 
                (c >= 'a' && c <= 'z') || 
                (c >= '0' && c <= '9') || 
                c == '-' || c == '.' || c == '_' || c == '~')
            {
                result.Append(c);
            }
            else
            {
                // Percent encode everything else
                var bytes = Encoding.UTF8.GetBytes(c.ToString());
                foreach (byte b in bytes)
                {
                    result.AppendFormat("%{0:X2}", b);
                }
            }
        }
        
        return result.ToString();
    }

    /// <summary>
    /// Generates a random nonce for OAuth requests
    /// </summary>
    public static string GenerateNonce()
    {
        return Guid.NewGuid().ToString("N");
    }

    /// <summary>
    /// Generates a timestamp for OAuth requests
    /// </summary>
    public static string GenerateTimestamp()
    {
        var unixEpoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        var timestamp = (long)(DateTime.UtcNow - unixEpoch).TotalSeconds;
        return timestamp.ToString();
    }
}
