using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Winmug.Core.Authentication;
using Winmug.Core.Services;

Console.WriteLine("Winmug Console Test");
Console.WriteLine("==================");

// Set up dependency injection
var services = new ServiceCollection();
services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information));

// Configure OAuth options with placeholder values
services.Configure<SmugMugOAuthOptions>(options =>
{
    options.ConsumerKey = "test-key";
    options.ConsumerSecret = "test-secret";
});

services.AddSingleton<ISecureCredentialStorage, WindowsCredentialStorage>();
services.AddHttpClient<ISmugMugApiClient, SmugMugApiClient>();
services.AddSingleton<ISmugMugAuthenticationService, SmugMugAuthenticationService>();
services.AddTransient<ISmugMugApiClient, SmugMugApiClient>();

var serviceProvider = services.BuildServiceProvider();

try
{
    Console.WriteLine("✓ Dependency injection setup successful");
    
    // Test OAuth signature generation
    var nonce = OAuthSignatureGenerator.GenerateNonce();
    var timestamp = OAuthSignatureGenerator.GenerateTimestamp();
    
    Console.WriteLine($"✓ OAuth nonce generated: {nonce[..8]}...");
    Console.WriteLine($"✓ OAuth timestamp generated: {timestamp}");
    
    // Test URL encoding
    var encoded = OAuthSignatureGenerator.UrlEncode("hello world@test");
    Console.WriteLine($"✓ URL encoding test: 'hello world@test' -> '{encoded}'");
    
    // Test authentication service creation
    var authService = serviceProvider.GetRequiredService<ISmugMugAuthenticationService>();
    Console.WriteLine($"✓ Authentication service created, IsAuthenticated: {authService.IsAuthenticated}");
    
    // Test API client creation
    var apiClient = serviceProvider.GetRequiredService<ISmugMugApiClient>();
    Console.WriteLine("✓ API client created successfully");
    
    Console.WriteLine();
    Console.WriteLine("🎉 All core components are working correctly!");
    Console.WriteLine();
    Console.WriteLine("To use the full application:");
    Console.WriteLine("1. Get your SmugMug API key from https://api.smugmug.com/api/developer/apply");
    Console.WriteLine("2. Update appsettings.json with your API credentials");
    Console.WriteLine("3. Run the WPF application: dotnet run --project src/Winmug");
}
catch (Exception ex)
{
    Console.WriteLine($"❌ Error: {ex.Message}");
    Console.WriteLine($"Details: {ex}");
    return 1;
}

return 0;
