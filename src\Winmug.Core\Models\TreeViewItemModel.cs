using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace Winmug.Core.Models;

/// <summary>
/// Represents an item in the tree view (folder or album) with hierarchical structure and selection state
/// </summary>
public class TreeViewItemModel : INotifyPropertyChanged
{
    private bool _isExpanded;
    private bool _isSelected;
    private bool? _isChecked;
    private string _name = string.Empty;
    private string _displayText = string.Empty;
    private TreeViewItemType _itemType;
    private bool _suppressParentUpdates = false;

    public TreeViewItemModel(TreeViewItemType itemType)
    {
        _itemType = itemType;
        Children = new ObservableCollection<TreeViewItemModel>();

        // Initialize checkbox to unchecked by default (false = unchecked, null = indeterminate, true = checked)
        _isChecked = false;

        // Suppress parent updates during initial construction
        _suppressParentUpdates = true;

        // Subscribe to children collection changes to update parent state
        Children.CollectionChanged += (s, e) =>
        {
            if (!_suppressParentUpdates)
            {
                UpdateParentState();
            }
        };
    }

    /// <summary>
    /// Type of tree view item (folder or album)
    /// </summary>
    public TreeViewItemType ItemType
    {
        get => _itemType;
        set
        {
            if (_itemType != value)
            {
                _itemType = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(Icon));
                OnPropertyChanged(nameof(IsFolder));
                OnPropertyChanged(nameof(IsAlbum));
            }
        }
    }

    /// <summary>
    /// Display name for the item
    /// </summary>
    public string Name
    {
        get => _name;
        set
        {
            if (_name != value)
            {
                _name = value;
                OnPropertyChanged();
                UpdateDisplayText();
            }
        }
    }

    /// <summary>
    /// Full display text including additional information
    /// </summary>
    public string DisplayText
    {
        get => _displayText;
        private set
        {
            if (_displayText != value)
            {
                _displayText = value;
                OnPropertyChanged();
            }
        }
    }

    /// <summary>
    /// Whether this item is expanded in the tree view
    /// </summary>
    public bool IsExpanded
    {
        get => _isExpanded;
        set
        {
            if (_isExpanded != value)
            {
                _isExpanded = value;
                OnPropertyChanged();
            }
        }
    }

    /// <summary>
    /// Whether this item is selected in the tree view
    /// </summary>
    public bool IsSelected
    {
        get => _isSelected;
        set
        {
            if (_isSelected != value)
            {
                _isSelected = value;
                OnPropertyChanged();
            }
        }
    }

    /// <summary>
    /// Checkbox state for selection (true = checked, false = unchecked)
    /// </summary>
    public bool? IsChecked
    {
        get => _isChecked;
        set
        {
            if (_isChecked != value)
            {
                // Force null values to false to prevent indeterminate state
                _isChecked = value ?? false;
                OnPropertyChanged();

                // Update children when parent changes
                if (IsFolder)
                {
                    UpdateChildrenCheckedState(_isChecked);
                }

                // Update parent when child changes (only if not suppressed)
                if (!_suppressParentUpdates)
                {
                    Parent?.UpdateParentState();
                }
            }
        }
    }

    /// <summary>
    /// Child items (folders and albums)
    /// </summary>
    public ObservableCollection<TreeViewItemModel> Children { get; }

    /// <summary>
    /// Parent item (null for root items)
    /// </summary>
    public TreeViewItemModel? Parent { get; set; }

    /// <summary>
    /// Associated folder node (for folders)
    /// </summary>
    public FolderNode? FolderNode { get; set; }

    /// <summary>
    /// Associated album (for albums)
    /// </summary>
    public SelectableAlbum? Album { get; set; }

    /// <summary>
    /// Icon to display for this item type
    /// </summary>
    public string Icon
    {
        get
        {
            return ItemType switch
            {
                TreeViewItemType.Folder => IsExpanded ? "📂" : "📁",
                TreeViewItemType.Album => Album?.AlbumTypeIcon ?? "📷",
                _ => "📄"
            };
        }
    }

    /// <summary>
    /// Whether this is a folder item
    /// </summary>
    public bool IsFolder => ItemType == TreeViewItemType.Folder;

    /// <summary>
    /// Whether this is an album item
    /// </summary>
    public bool IsAlbum => ItemType == TreeViewItemType.Album;

    /// <summary>
    /// Number of images in this item (album count for albums, total for folders)
    /// </summary>
    public int ImageCount
    {
        get
        {
            return ItemType switch
            {
                TreeViewItemType.Album => Album?.ImageCount ?? 0,
                TreeViewItemType.Folder => FolderNode?.TotalImageCount ?? 0,
                _ => 0
            };
        }
    }

    /// <summary>
    /// Estimated size in bytes
    /// </summary>
    public long EstimatedSizeBytes
    {
        get
        {
            return ItemType switch
            {
                TreeViewItemType.Album => Album?.EstimatedSizeBytes ?? 0,
                TreeViewItemType.Folder => FolderNode?.TotalEstimatedSizeBytes ?? 0,
                _ => 0
            };
        }
    }

    /// <summary>
    /// Human-readable size
    /// </summary>
    public string EstimatedSize => FormatBytes(EstimatedSizeBytes);

    /// <summary>
    /// Update the display text based on current properties
    /// </summary>
    private void UpdateDisplayText()
    {
        if (ItemType == TreeViewItemType.Folder)
        {
            DisplayText = $"{Name} ({ImageCount} images, {EstimatedSize})";
        }
        else if (ItemType == TreeViewItemType.Album)
        {
            DisplayText = $"{Name} ({ImageCount} images, {EstimatedSize})";
        }
        else
        {
            DisplayText = Name;
        }
    }

    /// <summary>
    /// Update children's checked state when parent changes
    /// </summary>
    private void UpdateChildrenCheckedState(bool? isChecked)
    {
        foreach (var child in Children)
        {
            child._isChecked = isChecked;
            child.OnPropertyChanged(nameof(IsChecked));
            
            if (child.IsFolder)
            {
                child.UpdateChildrenCheckedState(isChecked);
            }
        }
    }

    /// <summary>
    /// Enable automatic parent state updates (call after tree construction is complete)
    /// </summary>
    public void EnableParentUpdates()
    {
        _suppressParentUpdates = false;

        // Recursively enable for all children
        foreach (var child in Children)
        {
            child.EnableParentUpdates();
        }
    }

    /// <summary>
    /// Update parent's checked state based on children
    /// </summary>
    private void UpdateParentState()
    {
        if (Parent == null || _suppressParentUpdates) return;

        var checkedChildren = Parent.Children.Count(c => c.IsChecked == true);
        var totalChildren = Parent.Children.Count;

        // Simple two-state logic: checked if all children are checked, unchecked otherwise
        Parent._isChecked = checkedChildren == totalChildren && totalChildren > 0;

        Parent.OnPropertyChanged(nameof(IsChecked));
        Parent.Parent?.UpdateParentState();
    }

    /// <summary>
    /// Format bytes into human-readable string
    /// </summary>
    private static string FormatBytes(long bytes)
    {
        if (bytes == 0) return "0 B";
        
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        int order = 0;
        double size = bytes;
        
        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size /= 1024;
        }
        
        return $"{size:0.##} {sizes[order]}";
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    public virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));

        // Update display text when relevant properties change
        if (propertyName == nameof(Name) || propertyName == nameof(ImageCount) || propertyName == nameof(EstimatedSizeBytes))
        {
            UpdateDisplayText();
        }

        // Update icon when expansion state changes
        if (propertyName == nameof(IsExpanded))
        {
            OnPropertyChanged(nameof(Icon));
        }
    }
}

/// <summary>
/// Type of tree view item
/// </summary>
public enum TreeViewItemType
{
    Folder,
    Album
}
