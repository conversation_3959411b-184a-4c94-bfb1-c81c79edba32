using System.Text.Json.Serialization;

namespace Winmug.Core.Models;

/// <summary>
/// Represents a SmugMug album image (photo or video) from the album images endpoint
/// This is different from SmugMugImage as it contains album-specific properties
/// </summary>
public class SmugMugAlbumImage
{
    [JsonPropertyName("ImageKey")]
    public string ImageKey { get; set; } = string.Empty;

    [JsonPropertyName("Title")]
    public string? Title { get; set; }

    [JsonPropertyName("Caption")]
    public string? Caption { get; set; }

    [JsonPropertyName("Keywords")]
    public string? Keywords { get; set; }

    [JsonPropertyName("KeywordArray")]
    public string[]? KeywordArray { get; set; }

    [JsonPropertyName("FileName")]
    public string? FileName { get; set; }

    [JsonPropertyName("Format")]
    public string? Format { get; set; }

    [JsonPropertyName("Date")]
    public DateTime? Date { get; set; }

    [JsonPropertyName("DateTimeUploaded")]
    public DateTime? DateTimeUploaded { get; set; }

    [JsonPropertyName("LastUpdated")]
    public DateTime? LastUpdated { get; set; }

    [JsonPropertyName("WebUri")]
    public string WebUri { get; set; } = string.Empty;

    [JsonPropertyName("Uri")]
    public string Uri { get; set; } = string.Empty;

    [JsonPropertyName("Hidden")]
    public bool? Hidden { get; set; }

    [JsonPropertyName("Watermark")]
    public string? Watermark { get; set; }

    [JsonPropertyName("Latitude")]
    public string? Latitude { get; set; }

    [JsonPropertyName("Longitude")]
    public string? Longitude { get; set; }

    [JsonPropertyName("Altitude")]
    public int? Altitude { get; set; }

    [JsonPropertyName("OriginalHeight")]
    public int? OriginalHeight { get; set; }

    [JsonPropertyName("OriginalWidth")]
    public int? OriginalWidth { get; set; }

    [JsonPropertyName("OriginalSize")]
    public long? OriginalSize { get; set; }

    [JsonPropertyName("Processing")]
    public bool? Processing { get; set; }

    [JsonPropertyName("UploadKey")]
    public string? UploadKey { get; set; }

    [JsonPropertyName("Collectable")]
    public bool? Collectable { get; set; }

    [JsonPropertyName("IsArchive")]
    public bool? IsArchive { get; set; }

    [JsonPropertyName("IsVideo")]
    public bool? IsVideo { get; set; }

    [JsonPropertyName("CanEdit")]
    public bool? CanEdit { get; set; }

    [JsonPropertyName("CanBuy")]
    public bool? CanBuy { get; set; }

    [JsonPropertyName("Protected")]
    public bool? Protected { get; set; }

    [JsonPropertyName("EZProject")]
    public bool? EZProject { get; set; }

    [JsonPropertyName("Watermarked")]
    public bool? Watermarked { get; set; }

    [JsonPropertyName("Serial")]
    public int? Serial { get; set; }

    /// <summary>
    /// Direct download URL for the archived (original) version of the image
    /// This is the primary URL to use for downloading images from SmugMug
    /// </summary>
    [JsonPropertyName("ArchivedUri")]
    public string? ArchivedUri { get; set; }

    /// <summary>
    /// Size of the archived image in bytes
    /// </summary>
    [JsonPropertyName("ArchivedSize")]
    public long? ArchivedSize { get; set; }

    /// <summary>
    /// MD5 hash of the archived image for verification
    /// </summary>
    [JsonPropertyName("ArchivedMD5")]
    public string? ArchivedMD5 { get; set; }

    [JsonPropertyName("Status")]
    public string? Status { get; set; }

    [JsonPropertyName("SubStatus")]
    public string? SubStatus { get; set; }

    [JsonPropertyName("CanShare")]
    public bool? CanShare { get; set; }

    [JsonPropertyName("Comments")]
    public bool? Comments { get; set; }

    [JsonPropertyName("ShowKeywords")]
    public bool? ShowKeywords { get; set; }

    [JsonPropertyName("ThumbnailUrl")]
    public string? ThumbnailUrl { get; set; }

    [JsonPropertyName("ComponentFileTypes")]
    public Dictionary<string, string[]>? ComponentFileTypes { get; set; }

    [JsonPropertyName("FormattedValues")]
    public Dictionary<string, object>? FormattedValues { get; set; }

    [JsonPropertyName("PreferredDisplayFileExtension")]
    public string? PreferredDisplayFileExtension { get; set; }

    [JsonPropertyName("AlbumKey")]
    public string? AlbumKey { get; set; }

    [JsonPropertyName("Movable")]
    public bool? Movable { get; set; }

    [JsonPropertyName("Origin")]
    public string? Origin { get; set; }

    [JsonPropertyName("Uris")]
    public SmugMugAlbumImageUris? Uris { get; set; }

    // Helper properties
    public string DisplayName => !string.IsNullOrEmpty(Title) ? Title : FileName ?? ImageKey;
    public string SafeFileName => GetSafeFileName();

    private string GetSafeFileName()
    {
        if (!string.IsNullOrEmpty(FileName))
        {
            return SanitizeFileName(FileName);
        }

        var extension = !string.IsNullOrEmpty(Format) ? $".{Format.ToLowerInvariant()}" : ".jpg";
        return SanitizeFileName($"{ImageKey}{extension}");
    }

    private static string SanitizeFileName(string fileName)
    {
        var invalidChars = Path.GetInvalidFileNameChars();
        foreach (var invalidChar in invalidChars)
        {
            fileName = fileName.Replace(invalidChar, '_');
        }
        return fileName;
    }

    /// <summary>
    /// Convert to SmugMugImage for compatibility with existing code
    /// </summary>
    public SmugMugImage ToSmugMugImage()
    {
        return new SmugMugImage
        {
            ImageKey = ImageKey,
            Title = Title,
            Caption = Caption,
            Keywords = Keywords,
            KeywordsArray = KeywordArray,
            FileName = FileName,
            Format = Format,
            Date = Date,
            LastUpdated = LastUpdated,
            WebUri = WebUri,
            Uri = Uri,
            Hidden = Hidden,
            Watermark = Watermark == "Yes",
            Latitude = double.TryParse(Latitude, out var lat) ? lat : null,
            Longitude = double.TryParse(Longitude, out var lng) ? lng : null,
            Altitude = Altitude,
            ArchivedUri = ArchivedUri,
            ArchivedSize = ArchivedSize,
            ArchivedMD5 = ArchivedMD5
        };
    }
}

/// <summary>
/// Contains URIs for SmugMug album image-related resources
/// </summary>
public class SmugMugAlbumImageUris
{
    [JsonPropertyName("Components")]
    public SmugMugUriInfo? Components { get; set; }

    [JsonPropertyName("LargestImage")]
    public SmugMugUriInfo? LargestImage { get; set; }

    [JsonPropertyName("ImageSizes")]
    public SmugMugUriInfo? ImageSizes { get; set; }

    [JsonPropertyName("ImageSizeDetails")]
    public SmugMugUriInfo? ImageSizeDetails { get; set; }

    [JsonPropertyName("ImageAlbum")]
    public SmugMugUriInfo? ImageAlbum { get; set; }

    [JsonPropertyName("ImageOwner")]
    public SmugMugUriInfo? ImageOwner { get; set; }

    [JsonPropertyName("ImageDownload")]
    public SmugMugUriInfo? ImageDownload { get; set; }

    [JsonPropertyName("AlbumImageDownload")]
    public SmugMugUriInfo? AlbumImageDownload { get; set; }
}
