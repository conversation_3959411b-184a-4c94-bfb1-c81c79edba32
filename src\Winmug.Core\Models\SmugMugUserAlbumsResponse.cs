using System.Text.Json.Serialization;

namespace Winmug.Core.Models;

/// <summary>
/// Response model for SmugMug user albums endpoint (/api/v2/user/{nickname}!albums)
/// Based on the actual JSON structure where albums are in Response.Album array
/// </summary>
public class SmugMugUserAlbumsResponse
{
    [JsonPropertyName("Response")]
    public SmugMugUserAlbumsResponseData? Response { get; set; }

    [JsonPropertyName("Request")]
    public SmugMugRequestInfo? Request { get; set; }

    [JsonPropertyName("Options")]
    public object? Options { get; set; }
}

/// <summary>
/// The Response data containing the Album array
/// </summary>
public class SmugMugUserAlbumsResponseData
{
    [JsonPropertyName("Album")]
    public List<SmugMugAlbum>? Album { get; set; }

    [JsonPropertyName("Uri")]
    public string? Uri { get; set; }

    [JsonPropertyName("Locator")]
    public string? Locator { get; set; }

    [JsonPropertyName("LocatorType")]
    public string? LocatorType { get; set; }

    // Pagination info (if present)
    [JsonPropertyName("Pages")]
    public SmugMugPagingInfo? Pages { get; set; }
}

/// <summary>
/// Request information from SmugMug API response
/// </summary>
public class SmugMugRequestInfo
{
    [JsonPropertyName("Version")]
    public string? Version { get; set; }

    [JsonPropertyName("Method")]
    public string? Method { get; set; }

    [JsonPropertyName("Uri")]
    public string? Uri { get; set; }
}


