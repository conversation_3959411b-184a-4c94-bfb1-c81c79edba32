# Winmug User Guide

## Getting Started

Winmug helps you download all your photos from SmugMug to your computer, preserving your album structure and getting the original quality images.

### What You Need

- A SmugMug account with photos
- A Windows computer (Windows 10 or 11)
- An internet connection
- Some free disk space for your photos

### What You DON'T Need

- ❌ API keys or developer accounts
- ❌ Technical knowledge
- ❌ Special SmugMug subscription

## Step-by-Step Instructions

### 1. Start the Application

1. Download and run Winmug
2. The main window will open showing your authentication status

### 2. Authenticate with SmugMug

1. Click the **"Authenticate with Smug<PERSON>ug"** button
2. Your web browser will open to SmugMug's website
3. **Log in with your regular SmugMug username and password**
   - Use the same credentials you use to access SmugMug normally
   - This is NOT an API key or special developer credential
4. Smug<PERSON><PERSON> will ask if you want to authorize "Winmug" to access your photos
   - **Important**: The app requests "Full" access to download your private photos
   - This is necessary to access albums that aren't public
5. Click **"Authorize"** or **"Allow"** to grant access to your private content
6. Smug<PERSON><PERSON> will show you a **6-digit verification code**
7. Copy this code (it looks like: 123456)
8. Go back to Winmug and paste the code in the "6-digit verification code" field
9. Click **"Complete Authentication"**

✅ **Success!** You should see "Authenticated as [your username]"

**Access Verification**: The app will automatically verify it has access to your private content. You should see a message like "✓ Private access verified" in the log. If you see a warning about "public access only," you may need to re-authenticate.

### 3. Choose Where to Save Your Photos

1. Click the **"Browse..."** button next to "Target Directory"
2. Choose a folder on your computer where you want to save your photos
   - Example: `C:\Users\<USER>\Pictures\SmugMug Photos`
   - Make sure you have enough free space!

### 4. Start Downloading

1. Click **"Start Download"**
2. Winmug will:
   - Discover all your albums and photos
   - Show you the total count
   - Start downloading everything
3. Watch the progress bars to see:
   - Overall progress (how many photos completed)
   - Current file being downloaded
   - Download speed and estimated time remaining

### 5. Monitor Progress

- **Status messages** show what's happening
- **Log window** shows detailed progress
- **Progress bars** show completion percentage
- You can **Pause** and **Resume** anytime
- You can **Cancel** if needed

## What Gets Downloaded

### Folder Structure
Your local folders will match your SmugMug organization:
```
Your Target Folder/
├── Family Photos/
│   ├── 2023 Vacation/
│   │   ├── IMG_001.jpg
│   │   └── IMG_002.jpg
│   └── Birthday Party/
│       └── IMG_003.jpg
└── Work Events/
    └── Conference 2023/
        └── IMG_004.jpg
```

### Photo Quality
- **Original quality** - the same files you uploaded to SmugMug
- **Original filenames** - keeps your original file names when possible
- **All formats** - JPG, PNG, RAW files, videos, etc.

## Troubleshooting

### Authentication Issues

**Problem**: "Authentication failed"
- **Solution**: Make sure you're using your SmugMug username/password (not email in some cases)
- **Solution**: Check that you clicked "Authorize" on the SmugMug page
- **Solution**: Make sure the verification code is exactly 6 digits

**Problem**: Browser doesn't open
- **Solution**: Copy the URL from the log and paste it into your browser manually

**Problem**: "Public access only" warning after authentication
- **Solution**: Re-authenticate and make sure you click "Authorize" for full access
- **Solution**: Check that you're authorizing the correct permissions (should be "Full" access)
- **Solution**: Some private albums may not be downloadable with limited access

### Download Issues

**Problem**: "Permission denied" or "Access denied"
- **Solution**: Choose a different target folder where you have write permissions
- **Solution**: Run the application as administrator (right-click → "Run as administrator")

**Problem**: Download is very slow
- **Solution**: This is normal for large libraries - SmugMug limits download speed
- **Solution**: Use Pause/Resume if you need to stop and continue later

**Problem**: Some photos failed to download
- **Solution**: Check the error log for specific issues
- **Solution**: Try running the download again - it will skip already downloaded files

### General Issues

**Problem**: Application won't start
- **Solution**: Make sure you have .NET 8.0 installed
- **Solution**: Check Windows compatibility (Windows 10/11 required)

**Problem**: Running out of disk space
- **Solution**: Choose a target folder on a drive with more space
- **Solution**: Consider downloading albums one at a time

## Tips and Best Practices

### Before You Start
- **Check available disk space** - photo libraries can be very large
- **Use a fast internet connection** - downloads can take hours for large libraries
- **Close other applications** - to free up system resources

### During Download
- **Don't close the application** - downloads will stop
- **Don't put computer to sleep** - downloads will pause
- **Use Pause if needed** - you can always resume later

### After Download
- **Verify your photos** - spot-check that everything downloaded correctly
- **Backup your downloads** - consider copying to an external drive
- **Organize if needed** - you can reorganize the downloaded folders

## Privacy and Security

### What Information is Stored
- **Access tokens** - stored securely on your computer to avoid re-authentication
- **Download preferences** - your target folder choice
- **No passwords** - your SmugMug password is never stored

### What Information is Shared
- **Nothing** - your photos are downloaded directly from SmugMug to your computer
- **No cloud storage** - everything stays on your local machine

### Revoking Access
You can revoke Winmug's access anytime:
1. Log in to SmugMug
2. Go to Account Settings → Privacy
3. Find "Winmug" in authorized applications
4. Click "Revoke Access"

## Support

### Getting Help
- Check this user guide first
- Look at the application's log messages for specific errors
- Check the project's GitHub page for known issues

### Reporting Problems
When reporting issues, please include:
- What you were trying to do
- What error message you saw
- Your Windows version
- Approximate size of your SmugMug library

## Frequently Asked Questions

**Q: How long will this take?**
A: Depends on your library size and internet speed. Could be minutes for small libraries or hours/days for very large ones.

**Q: Will this use up my SmugMug bandwidth?**
A: Downloads count against any bandwidth limits on your SmugMug plan.

**Q: Can I download just specific albums?**
A: Currently, the app downloads everything. Selective downloading may be added in future versions.

**Q: What if I add new photos to SmugMug later?**
A: You can run the download again - it will skip existing files and only download new ones.

**Q: Is this safe?**
A: Yes! You're authenticating directly with SmugMug using their official OAuth system. Your credentials are never stored.

**Q: Does this work with SmugMug Pro/Business accounts?**
A: Yes! It works with all SmugMug account types.

**Q: What's the difference between "Public" and "Full" access?**
A:
- **Public access**: Only downloads photos/albums marked as publicly visible
- **Full access**: Downloads all your content including private albums and photos
- This app requests "Full" access so it can download your entire library

**Q: Can I download only public photos?**
A: The app is designed to download everything. If you only want public content, you could manually set albums to public first, but the app will still request full access.

**Q: How do I know if I have the right access level?**
A: After authentication, check the log messages. You should see "✓ Private access verified" if everything is working correctly.
