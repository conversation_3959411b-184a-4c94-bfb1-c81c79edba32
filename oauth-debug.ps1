# OAuth Debug Script for SmugMug
# This script tests OAuth signature generation and shows detailed error information

Write-Host "SmugMug OAuth Debug Test" -ForegroundColor Green
Write-Host "========================" -ForegroundColor Green
Write-Host ""

# Read configuration
$configPath = "src/Winmug/appsettings.json"
if (-not (Test-Path $configPath)) {
    Write-Host "Error: Configuration file not found at $configPath" -ForegroundColor Red
    exit 1
}

$config = Get-Content $configPath | ConvertFrom-Json
$consumerKey = $config.SmugMugOAuth.ConsumerKey
$consumerSecret = $config.SmugMugOAuth.ConsumerSecret

Write-Host "Consumer Key: $($consumerKey.Substring(0, 8))..." -ForegroundColor Yellow
Write-Host "Testing OAuth request token..." -ForegroundColor Yellow
Write-Host ""

# OAuth parameters
$timestamp = [int][double]::Parse((Get-Date -UFormat %s))
$nonce = [System.Guid]::NewGuid().ToString("N")
$url = "https://secure.smugmug.com/services/oauth/1.0a/getRequestToken"

Write-Host "OAuth Parameters:" -ForegroundColor Cyan
Write-Host "  oauth_callback: oob"
Write-Host "  oauth_consumer_key: $($consumerKey.Substring(0, 8))..."
Write-Host "  oauth_nonce: $nonce"
Write-Host "  oauth_signature_method: HMAC-SHA1"
Write-Host "  oauth_timestamp: $timestamp"
Write-Host "  oauth_version: 1.0"
Write-Host ""

# Create signature base string (simplified)
$params = @{
    "oauth_callback" = "oob"
    "oauth_consumer_key" = $consumerKey
    "oauth_nonce" = $nonce
    "oauth_signature_method" = "HMAC-SHA1"
    "oauth_timestamp" = $timestamp
    "oauth_version" = "1.0"
}

# Sort parameters
$sortedParams = $params.GetEnumerator() | Sort-Object Key
$paramString = ($sortedParams | ForEach-Object { "$($_.Key)=$($_.Value)" }) -join "&"

Write-Host "Parameter String: $paramString" -ForegroundColor Cyan
Write-Host ""

# Make HTTP request
try {
    $headers = @{
        "Authorization" = "OAuth oauth_callback=`"oob`", oauth_consumer_key=`"$consumerKey`", oauth_nonce=`"$nonce`", oauth_signature_method=`"HMAC-SHA1`", oauth_timestamp=`"$timestamp`", oauth_version=`"1.0`", oauth_signature=`"PLACEHOLDER`""
        "User-Agent" = "Winmug-Debug/1.0"
    }
    
    Write-Host "Making request to: $url" -ForegroundColor Yellow
    Write-Host "Authorization header (partial): OAuth oauth_callback=..." -ForegroundColor Cyan
    Write-Host ""
    
    $response = Invoke-WebRequest -Uri $url -Method POST -Headers $headers -ErrorAction Stop
    
    Write-Host "SUCCESS!" -ForegroundColor Green
    Write-Host "Status: $($response.StatusCode) $($response.StatusDescription)"
    Write-Host "Response: $($response.Content)"
    
} catch {
    Write-Host "FAILED!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        $statusDescription = $_.Exception.Response.StatusDescription
        
        Write-Host ""
        Write-Host "HTTP Response Details:" -ForegroundColor Yellow
        Write-Host "  Status: $statusCode $statusDescription"
        
        try {
            $responseStream = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($responseStream)
            $responseBody = $reader.ReadToEnd()
            Write-Host "  Response Body: $responseBody"
        } catch {
            Write-Host "  Could not read response body"
        }
        
        Write-Host ""
        Write-Host "Possible Issues:" -ForegroundColor Yellow
        
        if ($statusCode -eq 401) {
            Write-Host "  • Invalid API credentials" -ForegroundColor Red
            Write-Host "  • OAuth signature is incorrect" -ForegroundColor Red
            Write-Host "  • System clock is out of sync" -ForegroundColor Red
            Write-Host "  • API key doesn't have OAuth permissions" -ForegroundColor Red
        } elseif ($statusCode -eq 403) {
            Write-Host "  • API key doesn't have required permissions" -ForegroundColor Red
        } elseif ($statusCode -eq 400) {
            Write-Host "  • OAuth parameters are malformed" -ForegroundColor Red
        } else {
            Write-Host "  • Network connectivity issues" -ForegroundColor Red
            Write-Host "  • SmugMug API endpoint problems" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "Debug Information:" -ForegroundColor Yellow
Write-Host "  Current Time: $(Get-Date)"
Write-Host "  Timestamp: $timestamp"
Write-Host "  URL: $url"
Write-Host "  Consumer Key: $($consumerKey.Substring(0, 8))..."
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Green
Write-Host "1. Verify your SmugMug API credentials are correct"
Write-Host "2. Check that your system clock is synchronized"
Write-Host "3. Ensure your API key has OAuth permissions in SmugMug developer portal"
Write-Host "4. Try the OAuth test in the main application"
