using System.Security.Cryptography;
using System.Text;
using System.Web;

namespace Winmug.Core.Authentication;

/// <summary>
/// Generates OAuth 1.0a signatures for SmugMug API requests
/// </summary>
public static class OAuthSignatureGenerator
{
    /// <summary>
    /// Generates an OAuth 1.0a signature for the given request
    /// </summary>
    public static string GenerateSignature(
        string httpMethod,
        string url,
        Dictionary<string, string> parameters,
        string consumerSecret,
        string? tokenSecret = null)
    {
        // Create the signature base string
        var signatureBaseString = CreateSignatureBaseString(httpMethod, url, parameters);

        // Create the signing key
        var signingKey = CreateSigningKey(consumerSecret, tokenSecret);

        // Debug logging (can be removed in production)
        System.Diagnostics.Debug.WriteLine($"OAuth Debug - HTTP Method: {httpMethod}");
        System.Diagnostics.Debug.WriteLine($"OAuth Debug - URL: {url}");
        System.Diagnostics.Debug.WriteLine($"OAuth Debug - Normalized URL: {NormalizeUrl(url)}");
        System.Diagnostics.Debug.WriteLine($"OAuth Debug - Parameters: {string.Join(", ", parameters.Select(kvp => $"{kvp.Key}={kvp.Value}"))}");
        System.Diagnostics.Debug.WriteLine($"OAuth Debug - Signature Base String: {signatureBaseString}");
        System.Diagnostics.Debug.WriteLine($"OAuth Debug - Signing Key: {signingKey.Substring(0, Math.Min(20, signingKey.Length))}...");

        // Generate the signature
        var signature = GenerateHmacSha1Signature(signatureBaseString, signingKey);
        System.Diagnostics.Debug.WriteLine($"OAuth Debug - Generated Signature: {signature}");

        return signature;
    }

    /// <summary>
    /// Creates the OAuth signature base string
    /// </summary>
    private static string CreateSignatureBaseString(
        string httpMethod,
        string url,
        Dictionary<string, string> parameters)
    {
        // Normalize the URL (remove query parameters, convert to lowercase scheme and host)
        var normalizedUrl = NormalizeUrl(url);
        
        // Sort and encode parameters
        var normalizedParameters = NormalizeParameters(parameters);
        
        // Combine into signature base string
        var signatureBaseString = $"{httpMethod.ToUpperInvariant()}&{UrlEncode(normalizedUrl)}&{UrlEncode(normalizedParameters)}";
        
        return signatureBaseString;
    }

    /// <summary>
    /// Normalizes the URL by removing query parameters and converting scheme/host to lowercase
    /// </summary>
    private static string NormalizeUrl(string url)
    {
        var uri = new Uri(url);
        var normalizedUrl = $"{uri.Scheme.ToLowerInvariant()}://{uri.Host.ToLowerInvariant()}";

        // Only include port if it's not the default port for the scheme
        if ((uri.Scheme.ToLowerInvariant() == "http" && uri.Port != 80) ||
            (uri.Scheme.ToLowerInvariant() == "https" && uri.Port != 443))
        {
            normalizedUrl += $":{uri.Port}";
        }

        // Include the path, but ensure it starts with /
        var path = uri.AbsolutePath;
        if (string.IsNullOrEmpty(path) || path == "/")
        {
            path = "/";
        }
        normalizedUrl += path;

        return normalizedUrl;
    }

    /// <summary>
    /// Normalizes OAuth parameters by sorting and encoding them
    /// </summary>
    private static string NormalizeParameters(Dictionary<string, string> parameters)
    {
        var sortedParameters = parameters
            .OrderBy(kvp => kvp.Key)
            .ThenBy(kvp => kvp.Value)
            .Select(kvp => $"{UrlEncode(kvp.Key)}={UrlEncode(kvp.Value)}")
            .ToArray();
        
        return string.Join("&", sortedParameters);
    }

    /// <summary>
    /// Creates the signing key for OAuth signature generation
    /// </summary>
    private static string CreateSigningKey(string consumerSecret, string? tokenSecret)
    {
        return $"{UrlEncode(consumerSecret)}&{UrlEncode(tokenSecret ?? string.Empty)}";
    }

    /// <summary>
    /// Generates HMAC-SHA1 signature
    /// </summary>
    private static string GenerateHmacSha1Signature(string signatureBaseString, string signingKey)
    {
        var keyBytes = Encoding.UTF8.GetBytes(signingKey);
        var dataBytes = Encoding.UTF8.GetBytes(signatureBaseString);
        
        using var hmac = new HMACSHA1(keyBytes);
        var hashBytes = hmac.ComputeHash(dataBytes);
        return Convert.ToBase64String(hashBytes);
    }

    /// <summary>
    /// URL encodes a string according to OAuth specification (RFC 3986)
    /// </summary>
    public static string UrlEncode(string value)
    {
        if (string.IsNullOrEmpty(value))
            return string.Empty;

        // OAuth 1.0a requires RFC 3986 encoding
        // Unreserved characters: A-Z a-z 0-9 - . _ ~
        var result = new StringBuilder();

        foreach (char c in value)
        {
            if ((c >= 'A' && c <= 'Z') ||
                (c >= 'a' && c <= 'z') ||
                (c >= '0' && c <= '9') ||
                c == '-' || c == '.' || c == '_' || c == '~')
            {
                result.Append(c);
            }
            else
            {
                // Encode as %XX where XX is the hex value
                var bytes = Encoding.UTF8.GetBytes(c.ToString());
                foreach (byte b in bytes)
                {
                    result.AppendFormat("%{0:X2}", b);
                }
            }
        }

        return result.ToString();
    }

    /// <summary>
    /// Generates a random nonce for OAuth requests
    /// </summary>
    public static string GenerateNonce()
    {
        return Guid.NewGuid().ToString("N");
    }

    /// <summary>
    /// Generates a timestamp for OAuth requests
    /// </summary>
    public static string GenerateTimestamp()
    {
        var unixEpoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        var timestamp = (long)(DateTime.UtcNow - unixEpoch).TotalSeconds;
        return timestamp.ToString();
    }
}
