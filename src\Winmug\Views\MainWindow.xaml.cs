using System.ComponentModel;
using System.Windows;
using Winmug.ViewModels;

namespace Winmug.Views;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private readonly MainWindowViewModel _viewModel;

    public MainWindow(MainWindowViewModel viewModel)
    {
        try
        {
            _viewModel = viewModel;
            InitializeComponent();
            DataContext = viewModel;

            // Initialize the view model with error handling
            Loaded += OnWindowLoaded;

            // Subscribe to property changes for window resizing
            _viewModel.PropertyChanged += OnViewModelPropertyChanged;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error initializing main window: {ex.Message}\n\nDetails: {ex}",
                "Window Initialization Error", MessageBoxButton.OK, MessageBoxImage.Error);
            throw;
        }
    }

    private async void OnWindowLoaded(object sender, RoutedEventArgs e)
    {
        try
        {
            await _viewModel.InitializeAsync();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error during window initialization: {ex.Message}\n\nDetails: {ex}",
                "Initialization Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void OnViewModelPropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        // Handle window resizing when content visibility changes
        if (e.PropertyName == nameof(_viewModel.IsFolderStructureLoaded) ||
            e.PropertyName == nameof(_viewModel.IsAuthenticated))
        {
            Dispatcher.BeginInvoke(() =>
            {
                // Reset SizeToContent to allow automatic resizing
                SizeToContent = SizeToContent.Manual;
                SizeToContent = SizeToContent.Height;

                // Ensure minimum size constraints
                if (ActualHeight < MinHeight)
                {
                    Height = MinHeight;
                }
                if (ActualWidth < MinWidth)
                {
                    Width = MinWidth;
                }
            });
        }
    }
}
