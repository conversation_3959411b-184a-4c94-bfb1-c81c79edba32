using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace Winmug.Converters
{
    /// <summary>
    /// Converts string values to Visibility. Returns Visible if string is not null/empty, Collapsed otherwise.
    /// </summary>
    public class StringToVisibilityConverter : IValueConverter
    {
        public static readonly StringToVisibilityConverter Instance = new();
        public static readonly StringToVisibilityConverter InverseInstance = new() { IsInverse = true };

        public bool IsInverse { get; set; }

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            var hasValue = !string.IsNullOrEmpty(value as string);
            
            if (IsInverse)
                hasValue = !hasValue;
                
            return hasValue ? Visibility.Visible : Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
