using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Windows;
using System.Windows.Data;
using Winmug.Core.Authentication;
using Winmug.Core.Services;
using Winmug.Core.Models;

namespace Winmug.ViewModels;

/// <summary>
/// Custom logger that captures log messages and displays them in the UI
/// </summary>
public class UILogger : ILogger
{
    private readonly Action<string> _addLogMessage;

    public UILogger(Action<string> addLogMessage)
    {
        _addLogMessage = addLogMessage;
    }

    public IDisposable? BeginScope<TState>(TState state) where TState : notnull => null;

    public bool IsEnabled(LogLevel logLevel) => true;

    public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
    {
        var message = formatter(state, exception);
        var prefix = logLevel switch
        {
            LogLevel.Error => "❌ ERROR",
            LogLevel.Warning => "⚠ WARNING",
            LogLevel.Information => "ℹ INFO",
            LogLevel.Debug => "🔍 DEBUG",
            _ => logLevel.ToString().ToUpper()
        };

        _addLogMessage($"{prefix}: {message}");

        if (exception != null)
        {
            _addLogMessage($"   Exception: {exception.Message}");
        }
    }
}

/// <summary>
/// ViewModel for the main application window
/// </summary>
public partial class MainWindowViewModel : ObservableObject
{
    private readonly ISmugMugAuthenticationService _authService;
    private readonly ISmugMugApiClient _apiClient;
    private readonly IDownloadManager _downloadManager;
    private readonly ILogger<MainWindowViewModel> _logger;

    [ObservableProperty]
    private bool _isAuthenticated;

    [ObservableProperty]
    private string? _userNickname;

    [ObservableProperty]
    private string _authenticationStatus = "Not authenticated";

    [ObservableProperty]
    private SmugMugUser? _currentUser;

    [ObservableProperty]
    private string? _userDisplayName;

    [ObservableProperty]
    private string? _userProfileImageUrl;

    [ObservableProperty]
    private bool _hasProfileImage;

    [ObservableProperty]
    private string _userStatusText = "Status: ready to start downloading";

    [ObservableProperty]
    private bool _isDownloading;

    [ObservableProperty]
    private string? _targetDirectory;

    [ObservableProperty]
    private bool _isOperationInProgress;

    [ObservableProperty]
    private bool _isWaitingForVerificationCode;

    [ObservableProperty]
    private FolderNode? _folderStructure;

    [ObservableProperty]
    private bool _isFolderStructureLoaded;

    [ObservableProperty]
    private string _statusMessage = "Ready";

    [ObservableProperty]
    private ObservableCollection<string> _logMessages = new();

    [ObservableProperty]
    private bool _canStartDownload;

    [ObservableProperty]
    private bool _canPauseDownload;

    [ObservableProperty]
    private bool _canResumeDownload;

    [ObservableProperty]
    private bool _canCancelDownload;

    [ObservableProperty]
    private double _overallProgress;

    [ObservableProperty]
    private double _currentFileProgress;

    [ObservableProperty]
    private string _progressText = string.Empty;

    [ObservableProperty]
    private string _downloadSpeed = string.Empty;

    [ObservableProperty]
    private string _estimatedTimeRemaining = string.Empty;

    // Progress tracking for folder structure loading
    [ObservableProperty]
    private bool _isLoadingFolderStructure;

    [ObservableProperty]
    private string _albumDiscoveryProgress = string.Empty;

    [ObservableProperty]
    private int _albumsFoundCount;

    [ObservableProperty]
    private int _albumsFoundMaximum = 100;

    // Album list for main window display (legacy flat view)
    [ObservableProperty]
    private ObservableCollection<SelectableAlbum> _albums = new();

    [ObservableProperty]
    private ICollectionView? _albumsView;

    // Tree view for hierarchical album display
    [ObservableProperty]
    private ObservableCollection<TreeViewItemModel> _albumTreeItems = new();

    [ObservableProperty]
    private bool _useTreeView = true;

    [ObservableProperty]
    private string _searchText = string.Empty;

    [ObservableProperty]
    private int _totalAlbumCount;

    [ObservableProperty]
    private int _selectedAlbumCount;

    [ObservableProperty]
    private string _totalSelectedSize = "0 B";

    [ObservableProperty]
    private long _totalSelectedImageCount;

    // Theme management
    [ObservableProperty]
    private bool _isDarkTheme = true;

    // Authentication flow state
    private string? _requestToken;
    private string? _requestTokenSecret;
    private string? _authorizationUrl;

    public MainWindowViewModel(
        ISmugMugAuthenticationService authService,
        ISmugMugApiClient apiClient,
        IDownloadManager downloadManager,
        ILogger<MainWindowViewModel> logger)
    {
        _authService = authService;
        _apiClient = apiClient;
        _downloadManager = downloadManager;
        _logger = logger;

        // Subscribe to authentication events
        _authService.AuthenticationStatusChanged += OnAuthenticationStatusChanged;

        // Subscribe to download events
        _downloadManager.ProgressUpdated += OnDownloadProgressUpdated;
        _downloadManager.StatusChanged += OnDownloadStatusChanged;
        _downloadManager.ErrorOccurred += OnDownloadErrorOccurred;

        // Initialize authentication state
        IsAuthenticated = _authService.IsAuthenticated;
        UpdateAuthenticationStatus();

        // Initialize album collection view
        InitializeAlbumCollectionView();

        // Initialize theme
        ApplyTheme();

        // Load stored credentials if available
        _ = Task.Run(async () =>
        {
            try
            {
                var hasStoredCredentials = await _authService.LoadStoredCredentialsAsync();
                if (hasStoredCredentials)
                {
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        AddLogMessage("✓ Stored credentials loaded successfully");
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to load stored credentials on startup");
            }
        });
    }



    [RelayCommand]
    private async Task InitiateAuthenticationAsync()
    {
        try
        {
            IsOperationInProgress = true;
            StatusMessage = "Starting authentication...";
            AddLogMessage("🚀 Starting SmugMug authentication...");
            AddLogMessage("This will open your browser and automatically capture the authentication.");

            // Try automatic authentication first
            try
            {
                var accessTokenResponse = await _authService.InitiateAutomaticAuthenticationAsync();

                UserNickname = accessTokenResponse.UserNickname;
                StatusMessage = $"Successfully authenticated as {UserNickname}";
                AddLogMessage($"🎉 Automatic authentication completed successfully!");
                AddLogMessage($"✓ Authenticated as: {UserNickname}");

                // Get user profile information
                await LoadUserProfileAsync();

                // Simplified - assume full access for now
                AddLogMessage("✅ Full access assumed - can access your private photos");

                IsWaitingForVerificationCode = false;
                return;
            }
            catch (Exception autoEx)
            {
                _logger.LogWarning(autoEx, "Automatic authentication failed, falling back to manual mode");
                AddLogMessage($"⚠️ Automatic authentication failed: {autoEx.Message}");
                AddLogMessage("Falling back to manual authentication mode...");
            }

            // Fall back to manual authentication
            AddLogMessage("Starting manual authentication process...");

            var requestTokenResponse = await _authService.InitiateAuthenticationAsync();

            _requestToken = requestTokenResponse.Token;
            _requestTokenSecret = requestTokenResponse.TokenSecret;
            _authorizationUrl = requestTokenResponse.AuthorizationUrl;

            AddLogMessage($"✓ Request token obtained successfully");
            AddLogMessage($"🔍 DEBUG: Full authorization URL: {_authorizationUrl}");

            // Check if the URL contains the required parameters
            if (_authorizationUrl.Contains("Access=Full"))
            {
                AddLogMessage("✓ URL contains Access=Full parameter");
            }
            else
            {
                AddLogMessage("❌ WARNING: URL missing Access=Full parameter!");
            }

            if (_authorizationUrl.Contains("Permissions=Read"))
            {
                AddLogMessage("✓ URL contains Permissions=Read parameter");
            }
            else
            {
                AddLogMessage("❌ WARNING: URL missing Permissions=Read parameter!");
            }

            // Try to open the authorization URL in the default browser with better error handling
            bool browserOpened = false;
            try
            {
                var processInfo = new ProcessStartInfo
                {
                    FileName = _authorizationUrl,
                    UseShellExecute = true
                };

                var process = Process.Start(processInfo);
                browserOpened = process != null;

                if (browserOpened)
                {
                    AddLogMessage("✓ Browser opened successfully");
                }
            }
            catch (Exception browserEx)
            {
                _logger.LogWarning(browserEx, "Failed to open browser automatically");
                AddLogMessage($"⚠ Could not open browser automatically: {browserEx.Message}");
            }

            if (!browserOpened)
            {
                // Fallback: Show the URL to the user
                AddLogMessage("Please manually copy and paste this URL into your browser:");
                AddLogMessage(_authorizationUrl);

                // Try to copy to clipboard
                try
                {
                    Clipboard.SetText(_authorizationUrl);
                    AddLogMessage("✓ URL copied to clipboard");
                }
                catch
                {
                    // Ignore clipboard errors
                }
            }

            StatusMessage = "Please log in to SmugMug in your browser and enter the verification code.";
            IsWaitingForVerificationCode = true;
            AddLogMessage("");
            AddLogMessage("🔐 MANUAL OAUTH AUTHENTICATION:");
            AddLogMessage("1. Log in to SmugMug with your username and password");
            AddLogMessage("2. Click 'Authorize' to grant FULL ACCESS to Winmug");
            AddLogMessage("3. Copy the 6-digit verification code");
            AddLogMessage("4. Paste it below and click 'Complete Authentication'");
            AddLogMessage("💡 This will provide detailed debugging information");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initiate authentication");
            StatusMessage = "Authentication failed. Please try again.";
            AddLogMessage($"❌ Authentication error: {ex.Message}");

            // Show more detailed error information
            if (ex.InnerException != null)
            {
                AddLogMessage($"   Inner error: {ex.InnerException.Message}");
            }

            MessageBox.Show($"Authentication failed: {ex.Message}\n\nPlease check your internet connection and try again.",
                "Authentication Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            IsOperationInProgress = false;
        }
    }

    [RelayCommand]
    private async Task CompleteAuthenticationAsync(string verificationCode)
    {
        // Validate inputs
        if (string.IsNullOrWhiteSpace(verificationCode))
        {
            MessageBox.Show("Please enter the 6-digit verification code from SmugMug.",
                "Missing Verification Code", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        if (_requestToken == null || _requestTokenSecret == null)
        {
            MessageBox.Show("Please click 'Authenticate with SmugMug' first to start the authentication process.",
                "Authentication Not Started", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        // Clean and validate verification code format
        var cleanCode = verificationCode.Trim().Replace("-", "").Replace(" ", "");
        if (cleanCode.Length != 6 || !cleanCode.All(char.IsDigit))
        {
            MessageBox.Show("The verification code should be exactly 6 digits. Please check and try again.",
                "Invalid Verification Code", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        try
        {
            IsOperationInProgress = true;
            StatusMessage = "Completing authentication...";
            AddLogMessage($"Completing authentication with verification code: {cleanCode}");

            var accessTokenResponse = await _authService.CompleteAuthenticationAsync(
                cleanCode, _requestToken, _requestTokenSecret);

            UserNickname = accessTokenResponse.UserNickname;
            StatusMessage = $"Successfully authenticated as {UserNickname}";
            AddLogMessage($"✓ Authentication completed successfully for user: {UserNickname}");

            // Get user profile information
            await LoadUserProfileAsync();

            // Simplified - assume full access for now
            AddLogMessage("✓ Full access assumed - can access your private photos");

            // Clear temporary authentication state
            _requestToken = null;
            _requestTokenSecret = null;
            _authorizationUrl = null;
            IsWaitingForVerificationCode = false;

            AddLogMessage("✓ Authentication process completed successfully!");
            AddLogMessage($"Access token: {accessTokenResponse.Token.Substring(0, 8)}...");
            AddLogMessage($"User authenticated: {UserNickname}");

            // OAuth signature generation is now handled directly in the authentication service

          
        }
        catch (HttpRequestException httpEx) when (httpEx.Message.Contains("401"))
        {
            _logger.LogError(httpEx, "Authentication failed with 401 Unauthorized");
            StatusMessage = "Authentication failed. Please try again.";
            AddLogMessage($"❌ Authentication failed: Invalid verification code or expired session");
            AddLogMessage("Please try the authentication process again from the beginning.");

            // Clear temporary state on 401 error
            _requestToken = null;
            _requestTokenSecret = null;
            _authorizationUrl = null;
            IsWaitingForVerificationCode = false;

            MessageBox.Show("Authentication failed. The verification code may be incorrect or expired.\n\nPlease click 'Authenticate with SmugMug' to start over.",
                "Authentication Failed", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to complete authentication");
            StatusMessage = "Authentication failed. Please try again.";
            AddLogMessage($"❌ Authentication completion error: {ex.Message}");

            // Show more detailed error information
            if (ex.InnerException != null)
            {
                AddLogMessage($"   Inner error: {ex.InnerException.Message}");
            }

            MessageBox.Show($"Authentication failed: {ex.Message}\n\nPlease try again or contact support if the problem persists.",
                "Authentication Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            IsOperationInProgress = false;
        }
    }

    [RelayCommand]
    private async Task LogoutAsync()
    {
        try
        {
            await _authService.ClearStoredCredentialsAsync();
            StatusMessage = "Logged out successfully";
            AddLogMessage("User logged out and credentials cleared");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to logout");
            AddLogMessage($"Logout error: {ex.Message}");
        }
    }

    [RelayCommand]
    private void ToggleTheme()
    {
        IsDarkTheme = !IsDarkTheme;
        AddLogMessage($"Theme switched to {(IsDarkTheme ? "Dark" : "Light")} mode");

        // Apply theme change to the application
        ApplyTheme();
    }

    [RelayCommand]
    private void ToggleTreeView()
    {
        UseTreeView = !UseTreeView;
        AddLogMessage($"View switched to {(UseTreeView ? "Tree" : "List")} mode");

        // Re-populate the view with the current folder structure
        if (FolderStructure != null)
        {
            PopulateAlbumsFromFolderStructure(FolderStructure);
        }
    }

    [RelayCommand]
    private void OpenUserGuide()
    {
        try
        {
            var userGuideUrl = "https://github.com/harikas14/Pages/blob/main/docs/WinMug-UserGuide.md"; 
            var processInfo = new ProcessStartInfo
            {
                FileName = userGuideUrl,
                UseShellExecute = true
            };
            Process.Start(processInfo);
            AddLogMessage("📖 User Guide opened in browser");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to open User Guide");
            AddLogMessage($"❌ Failed to open User Guide: {ex.Message}");
        }
    }

    [RelayCommand]
    private void OpenGitHub()
    {
        try
        {
            var githubUrl = "https://github.com/harikas14"; 
            var processInfo = new ProcessStartInfo
            {
                FileName = githubUrl,
                UseShellExecute = true
            };
            Process.Start(processInfo);
            AddLogMessage("🐙 GitHub repository opened in browser");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to open GitHub");
            AddLogMessage($"❌ Failed to open GitHub: {ex.Message}");
        }
    }

    private void ApplyTheme()
    {
        try
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                var app = Application.Current;
                var existingDictionary = app.Resources.MergedDictionaries.FirstOrDefault(d =>
                    d.Source?.OriginalString?.Contains("AppStyles.xaml") == true);

                if (existingDictionary != null)
                {
                    app.Resources.MergedDictionaries.Remove(existingDictionary);
                }

                // Load the appropriate theme
                var themeUri = IsDarkTheme
                    ? new Uri("Styles/DarkTheme.xaml", UriKind.Relative)
                    : new Uri("Styles/LightTheme.xaml", UriKind.Relative);

                var newDictionary = new ResourceDictionary { Source = themeUri };
                app.Resources.MergedDictionaries.Add(newDictionary);
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to apply theme");
            AddLogMessage($"Failed to apply theme: {ex.Message}");
        }
    }

    [RelayCommand]
    private void SelectTargetDirectory()
    {
        var dialog = new Microsoft.Win32.OpenFolderDialog
        {
            Title = "Select Download Directory",
            Multiselect = false
        };

        if (dialog.ShowDialog() == true)
        {
            TargetDirectory = dialog.FolderName;
            AddLogMessage($"Target directory selected: {TargetDirectory}");

            // Update button states when target directory changes
            UpdateDownloadButtonStates();
        }
    }

    [RelayCommand]
    private async Task LoadFolderStructureAsync()
    {
        if (!IsAuthenticated)
        {
            AddLogMessage("❌ Please authenticate first before loading folder structure");
            MessageBox.Show("Please authenticate first before loading folder structure", "Not Authenticated", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        try
        {
            IsOperationInProgress = true;
            IsLoadingFolderStructure = true;
            AlbumsFoundCount = 0;
            AlbumsFoundMaximum = 100; // Start with a reasonable estimate
            AlbumDiscoveryProgress = "Initializing...";
            StatusMessage = "Loading folder structure...";
            AddLogMessage("=== STARTING FOLDER STRUCTURE LOAD ===");
            AddLogMessage($"Authentication status: {IsAuthenticated}");
            AddLogMessage($"User nickname: {UserNickname ?? "Unknown"}");
            AddLogMessage($"DEBUG: Progress tracking initialized - AlbumsFoundCount: {AlbumsFoundCount}, AlbumsFoundMaximum: {AlbumsFoundMaximum}");

            // Start directly with /api/v2!authuser to get all authenticated user URLs
            AddLogMessage("Getting authenticated user information from /api/v2!authuser...");
            AlbumDiscoveryProgress = "Getting user information...";

            SmugMugUser user;
            try
            {
                user = _apiClient.GetAuthenticatedUser();
                AddLogMessage($"✓ Successfully retrieved user information:");
                AddLogMessage($"  User: {user.Name} ({user.NickName})");
                AddLogMessage($"  User URI: {user.Uri}");

                // Log available URIs from the authuser response
                if (user.Uris != null)
                {
                    AddLogMessage("✓ Available API endpoints from /api/v2!authuser:");
                    if (user.Uris.Node?.Uri != null)
                        AddLogMessage($"  Node URI: {user.Uris.Node.Uri}");
                    if (user.Uris.Folder?.Uri != null)
                        AddLogMessage($"  Folder URI: {user.Uris.Folder.Uri}");
                    if (user.Uris.UserProfile?.Uri != null)
                        AddLogMessage($"  UserProfile URI: {user.Uris.UserProfile.Uri}");

                    // Check access level based on available URIs
                    if (user.Uris.Node?.Uri != null && user.Uris.Folder?.Uri != null)
                    {
                        AddLogMessage("✅ FULL ACCESS CONFIRMED - Node and Folder endpoints available");
                        AddLogMessage("✅ Can access folder structure and private content");
                    }
                    else
                    {
                        AddLogMessage("⚠️ LIMITED ACCESS - Missing Node or Folder endpoints");
                        AddLogMessage("  This may limit access to private content and folder structure");
                    }
                }
                else
                {
                    AddLogMessage("⚠️ No URIs found in authuser response - this indicates limited access");
                }
            }
            catch (Exception ex)
            {
                AddLogMessage($"❌ Failed to get authenticated user information: {ex.Message}");
                throw;
            }

            // Now load the complete folder structure using the URIs from authuser
            AddLogMessage("Loading complete folder structure using authenticated URIs...");
            AlbumDiscoveryProgress = "Discovering albums...";
            AddLogMessage("DEBUG: About to start Task.Run for folder structure loading...");

            try
            {
                AddLogMessage("DEBUG: Starting async folder structure loading...");

                // Update progress to show we're starting
                AlbumDiscoveryProgress = "Starting discovery...";

                // Use proper async/await to prevent deadlocks
                AddLogMessage("DEBUG: Starting async folder structure loading...");
                AlbumDiscoveryProgress = "Connecting to SmugMug...";
                AlbumsFoundCount = 0;
                AlbumsFoundMaximum = 100;

                try
                {
                    AddLogMessage("DEBUG: About to call _apiClient.GetFolderStructureAsync()...");

                    // Call the async API method directly - no Task.Run needed
                    FolderStructure = await _apiClient.GetFolderStructureAsync();

                    AddLogMessage("DEBUG: GetFolderStructureAsync() returned successfully!");
                    AddLogMessage($"DEBUG: Result is null: {FolderStructure == null}");

                    if (FolderStructure != null)
                    {
                        var totalAlbums = CountTotalAlbums(FolderStructure);
                        AddLogMessage($"DEBUG: Total albums counted: {totalAlbums}");
                        AddLogMessage($"DEBUG: Root folder name: {FolderStructure.Name}");
                        AddLogMessage($"DEBUG: Root folder has {FolderStructure.Albums.Count} direct albums");
                        AddLogMessage($"DEBUG: Root folder has {FolderStructure.Children.Count} child folders");

                        AlbumsFoundCount = totalAlbums;
                        AlbumsFoundMaximum = totalAlbums;
                        AlbumDiscoveryProgress = $"Complete! Found {totalAlbums} albums";
                    }
                    else
                    {
                        AddLogMessage("ERROR: GetFolderStructureAsync() returned null!");
                        AlbumDiscoveryProgress = "Error: No data returned";
                    }
                }
                catch (Exception ex)
                {
                    AddLogMessage($"ERROR: Exception in GetFolderStructureAsync(): {ex.Message}");
                    AddLogMessage($"ERROR: Exception type: {ex.GetType().Name}");
                    AddLogMessage($"ERROR: Stack trace: {ex.StackTrace}");
                    AlbumDiscoveryProgress = $"Error: {ex.Message}";
                    throw;
                }

                IsFolderStructureLoaded = true;

                // Set final progress values
                var finalAlbumCount = FolderStructure != null ? CountTotalAlbums(FolderStructure) : 0;
                AlbumsFoundCount = finalAlbumCount;
                AlbumsFoundMaximum = finalAlbumCount; // Set max to actual count for 100% completion

                StatusMessage = "Folder structure loaded successfully";
                AlbumDiscoveryProgress = $"Complete! Found {finalAlbumCount} albums";
                AddLogMessage($"✓ Folder structure loaded successfully!");
                if (FolderStructure != null)
                {
                    AddLogMessage($"  Total images: {FolderStructure.TotalImageCount}");
                    AddLogMessage($"  Total folders: {CountTotalFolders(FolderStructure)}");
                    AddLogMessage($"  Total albums: {finalAlbumCount}");
                    AddLogMessage($"  Total estimated size: {FolderStructure.TotalEstimatedSize}");

                    // Populate albums for main window display
                    PopulateAlbumsFromFolderStructure(FolderStructure);
                }
                AddLogMessage("=== FOLDER STRUCTURE LOAD COMPLETE ===");
            }
            catch (Exception ex)
            {
                AddLogMessage($"❌ Folder structure loading failed: {ex.Message}");
                throw;
            }
        }
        catch (HttpRequestException httpEx) when (httpEx.Message.Contains("401") || httpEx.Message.Contains("Unauthorized"))
        {
            _logger.LogError(httpEx, "Authentication failed when loading folder structure");
            StatusMessage = "Authentication failed";
            AddLogMessage("❌ AUTHENTICATION FAILURE DETECTED");
            AddLogMessage($"  Error: {httpEx.Message}");
            AddLogMessage("  This usually means:");
            AddLogMessage("  1. Your session has expired");
            AddLogMessage("  2. You have limited access permissions");
            AddLogMessage("  3. The access token is invalid");
            AddLogMessage("");
            AddLogMessage("SOLUTION: Click 'Logout' then 'Authenticate with SmugMug' to get fresh credentials");

            MessageBox.Show("Authentication failed. Your session may have expired or you have limited access permissions.\n\nPlease logout and authenticate again to get full access.",
                "Authentication Error", MessageBoxButton.OK, MessageBoxImage.Warning);
        }
        catch (InvalidOperationException invOpEx) when (invOpEx.Message.Contains("OAuth permissions") || invOpEx.Message.Contains("limited access"))
        {
            _logger.LogError(invOpEx, "OAuth permission issue when loading folder structure");
            StatusMessage = "Limited access permissions";
            AddLogMessage("❌ LIMITED ACCESS PERMISSIONS DETECTED");
            AddLogMessage($"  Error: {invOpEx.Message}");
            AddLogMessage("  This means you have limited access to SmugMug.");
            AddLogMessage("  To access your folder structure:");
            AddLogMessage("  1. Click 'Logout' to clear current credentials");
            AddLogMessage("  2. Click 'Authenticate with SmugMug' to start fresh");
            AddLogMessage("  3. When prompted, make sure to click 'Authorize' or 'Allow'");
            AddLogMessage("  4. Grant full access permissions when asked");
            AddLogMessage("");
            AddLogMessage("SOLUTION: Re-authenticate with full access permissions");

            MessageBox.Show("You have limited access to SmugMug and cannot access your folder structure.\n\nTo fix this:\n1. Click 'Logout' to clear current credentials\n2. Click 'Authenticate with SmugMug' to start fresh\n3. When prompted, make sure to grant full access permissions",
                "Limited Access Permissions", MessageBoxButton.OK, MessageBoxImage.Warning);
        }
        catch (UriFormatException uriEx)
        {
            _logger.LogError(uriEx, "URI format exception when loading folder structure");
            StatusMessage = "Invalid user data from SmugMug";
            AddLogMessage("❌ INVALID USER DATA DETECTED");
            AddLogMessage($"  Error: {uriEx.Message}");
            AddLogMessage("  This indicates a problem with the user data from SmugMug.");
            AddLogMessage("  This usually happens with limited access permissions.");
            AddLogMessage("");
            AddLogMessage("SOLUTION: Re-authenticate with full access permissions");

            MessageBox.Show("Invalid user data received from SmugMug. This usually indicates limited access permissions.\n\nPlease logout and authenticate again with full access permissions.",
                "Invalid User Data", MessageBoxButton.OK, MessageBoxImage.Warning);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load folder structure");
            StatusMessage = "Failed to load folder structure";
            AddLogMessage($"❌ UNEXPECTED ERROR: {ex.Message}");
            AddLogMessage($"  Exception type: {ex.GetType().Name}");

            if (ex.InnerException != null)
            {
                AddLogMessage($"  Inner error: {ex.InnerException.Message}");
                AddLogMessage($"  Inner exception type: {ex.InnerException.GetType().Name}");
            }

            AddLogMessage($"  Stack trace: {ex.StackTrace}");

            MessageBox.Show($"Failed to load folder structure: {ex.Message}\n\nCheck the logs for detailed error information.",
                "Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            IsOperationInProgress = false;
            IsLoadingFolderStructure = false;
            // Keep the final progress message visible
        }
    }

    /// <summary>
    /// Get folder structure with progress reporting
    /// </summary>
    private FolderNode GetFolderStructureWithProgress(IProgress<(int albumCount, string message)> progress)
    {
        progress.Report((0, "DEBUG: GetFolderStructureWithProgress method called"));
        // Create a custom API client wrapper that reports progress
        var progressReporter = new ProgressReporter(progress);

        // Modify the API client to use our progress reporter
        progress.Report((0, "DEBUG: About to call GetFolderStructureWithProgressInternal"));
        return GetFolderStructureWithProgressInternal(progressReporter);
    }

    /// <summary>
    /// Internal method to get folder structure with progress reporting
    /// </summary>
    private FolderNode GetFolderStructureWithProgressInternal(ProgressReporter progressReporter)
    {
        progressReporter.Report(0, "Starting album discovery...");

        // Use reflection to call the progress-enabled method in the API client
        // This is a temporary solution until we can modify the interface
        var apiClientType = _apiClient.GetType();
        var method = apiClientType.GetMethod("BuildFolderStructureFromUserAlbumsSimpleWithProgress",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

        if (method != null)
        {
            // Get user info first
            var user = _apiClient.GetAuthenticatedUser();
            var userAlbumsUri = $"/api/v2/user/{user.NickName}!albums";

            // Create progress callback
            Action<int, string> progressCallback = (count, message) =>
            {
                progressReporter.Report(count, message);
            };

            // Call the progress-enabled method
            var result = (FolderNode?)method.Invoke(_apiClient, new object[] { userAlbumsUri, user.NickName, progressCallback, CancellationToken.None });

            if (result != null)
            {
                // Count total albums for final progress report
                var totalAlbums = CountTotalAlbums(result);
                progressReporter.Report(totalAlbums, $"Discovery complete! Found {totalAlbums} albums");

                return result;
            }
            else
            {
                throw new InvalidOperationException("Failed to get folder structure from API client");
            }
        }
        else
        {
            // Fallback to regular method if reflection fails
            progressReporter.Report(0, "Using fallback method...");
            var result = _apiClient.GetFolderStructure();
            var totalAlbums = CountTotalAlbums(result);
            progressReporter.Report(totalAlbums, $"Discovery complete! Found {totalAlbums} albums");
            return result;
        }
    }

    /// <summary>
    /// Helper class for progress reporting
    /// </summary>
    private class ProgressReporter
    {
        private readonly IProgress<(int albumCount, string message)> _progress;

        public ProgressReporter(IProgress<(int albumCount, string message)> progress)
        {
            _progress = progress;
        }

        public void Report(int albumCount, string message)
        {
            _progress?.Report((albumCount, message));
        }
    }

    /// <summary>
    /// Count total albums in the folder structure
    /// </summary>
    private int CountTotalAlbums(FolderNode folder)
    {
        var count = folder.Albums.Count;
        foreach (var child in folder.Children)
        {
            count += CountTotalAlbums(child);
        }
        return count;
    }

    private int CountTotalFolders(FolderNode folder)
    {
        int count = 1; // Count this folder
        foreach (var child in folder.Children)
        {
            count += CountTotalFolders(child);
        }
        return count;
    }



    [RelayCommand]
    private void SelectAlbumsForDownload()
    {
        if (!IsAuthenticated)
        {
            MessageBox.Show("Please authenticate first.", "Not Authenticated",
                MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        try
        {
            // Create and show album selection window
            var albumSelectionLogger = new UILogger(AddLogMessage);
            var albumSelectionViewModel = new AlbumSelectionViewModel(_apiClient, albumSelectionLogger);
            var albumSelectionWindow = new Views.AlbumSelectionWindow(albumSelectionViewModel)
            {
                Owner = Application.Current.MainWindow
            };

            if (albumSelectionWindow.ShowDialog() == true)
            {
                var selectedAlbums = albumSelectionWindow.SelectedAlbums;
                if (selectedAlbums.Count > 0)
                {
                    AddLogMessage($"Selected {selectedAlbums.Count} albums for download");

                    // Start selective download
                    _ = Task.Run(async () => await StartSelectiveDownloadAsync(selectedAlbums));
                }
                else
                {
                    AddLogMessage("No albums selected for download");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to open album selection");
            AddLogMessage($"Failed to open album selection: {ex.Message}");
            MessageBox.Show($"Failed to open album selection: {ex.Message}", "Error",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    [RelayCommand]
    private async Task StartDownloadAsync()
    {
        _logger.LogDebug("StartDownloadAsync called");
        AddLogMessage("🔍 DEBUG: StartDownloadAsync method called");

        _logger.LogDebug("Checking target directory: {TargetDirectory}", TargetDirectory ?? "null");
        if (string.IsNullOrEmpty(TargetDirectory))
        {
            _logger.LogWarning("Target directory not set");
            AddLogMessage("❌ Target directory not set");
            MessageBox.Show("Please select a target directory first.", "No Target Directory",
                MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        _logger.LogDebug("Checking authentication status: {IsAuthenticated}", IsAuthenticated);
        if (!IsAuthenticated)
        {
            _logger.LogWarning("User not authenticated");
            AddLogMessage("❌ User not authenticated");
            MessageBox.Show("Please authenticate first.", "Not Authenticated",
                MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        // Check if there are selected albums in the main window
        List<SelectableAlbum> selectedAlbums;
        if (UseTreeView)
        {
            selectedAlbums = GetSelectedAlbumsFromTree(AlbumTreeItems);
            _logger.LogDebug("Selected albums from tree view: {SelectedCount}", selectedAlbums.Count);
            AddLogMessage($"🔍 DEBUG: Found {selectedAlbums.Count} selected albums from tree view out of {TotalAlbumCount} total albums");
        }
        else
        {
            _logger.LogDebug("Total albums in collection: {AlbumCount}", Albums.Count);
            selectedAlbums = Albums.Where(a => a.IsSelected).ToList();
            _logger.LogDebug("Selected albums count: {SelectedCount}", selectedAlbums.Count);
            AddLogMessage($"🔍 DEBUG: Found {selectedAlbums.Count} selected albums out of {Albums.Count} total albums");
        }

        if (selectedAlbums.Count == 0)
        {
            _logger.LogWarning("No albums selected for download");
            AddLogMessage("❌ No albums selected for download");
            MessageBox.Show("Please select at least one album to download.", "No Albums Selected",
                MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        try
        {
            _logger.LogInformation("Starting download of {SelectedCount} albums to {TargetDirectory}", selectedAlbums.Count, TargetDirectory);
            IsOperationInProgress = true;
            UpdateDownloadButtonStates();
            AddLogMessage($"Starting download of {selectedAlbums.Count} selected albums...");

            // Log selected album details
            foreach (var album in selectedAlbums.Take(5)) // Log first 5 albums
            {
                _logger.LogDebug("Selected album: {AlbumName} (Key: {AlbumKey}, Images: {ImageCount})",
                    album.Name, album.AlbumKey, album.ImageCount);
            }
            if (selectedAlbums.Count > 5)
            {
                _logger.LogDebug("... and {RemainingCount} more albums", selectedAlbums.Count - 5);
            }

            // Use selective download with the selected albums from the main window
            await _downloadManager.StartSelectiveDownloadAsync(TargetDirectory, selectedAlbums);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Download failed");
            AddLogMessage($"Download failed: {ex.Message}");
            MessageBox.Show($"Download failed: {ex.Message}", "Download Error",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            IsOperationInProgress = false;
            UpdateDownloadButtonStates();
            _logger.LogDebug("StartDownloadAsync completed");
        }
    }

    private async Task StartSelectiveDownloadAsync(List<SelectableAlbum> selectedAlbums)
    {
        if (string.IsNullOrEmpty(TargetDirectory))
        {
            MessageBox.Show("Please select a target directory first.", "No Target Directory",
                MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        try
        {
            IsOperationInProgress = true;
            UpdateDownloadButtonStates();
            AddLogMessage($"Starting selective download of {selectedAlbums.Count} albums...");

            await _downloadManager.StartSelectiveDownloadAsync(TargetDirectory, selectedAlbums);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Selective download failed");
            AddLogMessage($"Selective download failed: {ex.Message}");
            MessageBox.Show($"Selective download failed: {ex.Message}", "Download Error",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            IsOperationInProgress = false;
            UpdateDownloadButtonStates();
        }
    }

    [RelayCommand]
    private async Task PauseDownloadAsync()
    {
        try
        {
            await _downloadManager.PauseAsync();
            AddLogMessage("Download paused");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to pause download");
            AddLogMessage($"Failed to pause download: {ex.Message}");
        }
    }

    [RelayCommand]
    private async Task ResumeDownloadAsync()
    {
        try
        {
            await _downloadManager.ResumeAsync();
            AddLogMessage("Download resumed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to resume download");
            AddLogMessage($"Failed to resume download: {ex.Message}");
        }
    }

    [RelayCommand]
    private async Task CancelDownloadAsync()
    {
        try
        {
            await _downloadManager.CancelAsync();
            AddLogMessage("Download cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cancel download");
            AddLogMessage($"Failed to cancel download: {ex.Message}");
        }
    }

    private void OnDownloadProgressUpdated(object? sender, DownloadProgressEventArgs e)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            var stats = e.Statistics;
            OverallProgress = stats.OverallProgressPercentage;
            ProgressText = $"{stats.DownloadedPhotos}/{stats.TotalPhotos} photos ({stats.ProcessedAlbums}/{stats.TotalAlbums} albums)";

            if (stats.AverageDownloadSpeed.HasValue)
            {
                DownloadSpeed = FormatBytes(stats.AverageDownloadSpeed.Value) + "/s";
            }

            if (stats.EstimatedTimeRemaining.HasValue)
            {
                EstimatedTimeRemaining = FormatTimeSpan(stats.EstimatedTimeRemaining.Value);
            }

            if (!string.IsNullOrEmpty(e.CurrentOperation))
            {
                StatusMessage = e.CurrentOperation;
            }
        });
    }

    private void OnDownloadStatusChanged(object? sender, DownloadStatusChangedEventArgs e)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            StatusMessage = e.Message ?? e.NewStatus.ToString();
            AddLogMessage($"Download status: {e.NewStatus} - {e.Message}");

            // Update IsDownloading based on status
            IsDownloading = e.NewStatus == DownloadStatus.Downloading ||
                           e.NewStatus == DownloadStatus.Discovering ||
                           e.NewStatus == DownloadStatus.Paused;

            UpdateDownloadButtonStates();

            if (e.NewStatus == DownloadStatus.Completed)
            {
                var summary = _downloadManager.GetDownloadSummary();
                ShowDownloadSummary(summary);
            }
        });
    }

    private void OnDownloadErrorOccurred(object? sender, DownloadErrorEventArgs e)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            var errorMessage = $"Error: {e.Exception.Message}";
            if (!string.IsNullOrEmpty(e.Context))
            {
                errorMessage = $"{e.Context}: {e.Exception.Message}";
            }
            AddLogMessage(errorMessage);
        });
    }

    private void UpdateDownloadButtonStates()
    {
        var status = _downloadManager.Status;
        var wasCanStartDownload = CanStartDownload;

        CanStartDownload = IsAuthenticated && !string.IsNullOrEmpty(TargetDirectory) &&
                          (status == DownloadStatus.NotStarted || status == DownloadStatus.Completed ||
                           status == DownloadStatus.Cancelled || status == DownloadStatus.Error);
        CanPauseDownload = status == DownloadStatus.Downloading;
        CanResumeDownload = status == DownloadStatus.Paused;
        CanCancelDownload = status == DownloadStatus.Downloading || status == DownloadStatus.Paused;

        // Log button state changes for debugging
        if (wasCanStartDownload != CanStartDownload)
        {
            _logger.LogDebug("CanStartDownload changed from {OldValue} to {NewValue}. Status: {Status}, IsAuthenticated: {IsAuthenticated}, TargetDirectory: {TargetDirectory}",
                wasCanStartDownload, CanStartDownload, status, IsAuthenticated, !string.IsNullOrEmpty(TargetDirectory));
            AddLogMessage($"🔍 DEBUG: Start Download button enabled: {CanStartDownload} (Status: {status}, Auth: {IsAuthenticated}, Dir: {!string.IsNullOrEmpty(TargetDirectory)})");
        }
    }

    private void OnAuthenticationStatusChanged(object? sender, AuthenticationStatusChangedEventArgs e)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            IsAuthenticated = e.IsAuthenticated;
            UserNickname = e.UserNickname;
            UpdateAuthenticationStatus();

            if (e.Error != null)
            {
                AddLogMessage($"Authentication error: {e.Error.Message}");
            }
        });
    }

    private void UpdateAuthenticationStatus()
    {
        AuthenticationStatus = IsAuthenticated 
            ? $"Authenticated as {UserNickname ?? "Unknown"}"
            : "Not authenticated";
    }

    private void AddLogMessage(string message)
    {
        var timestamp = DateTime.Now.ToString("HH:mm:ss");
        LogMessages.Add($"[{timestamp}] {message}");
        
        // Keep only the last 100 messages
        while (LogMessages.Count > 100)
        {
            LogMessages.RemoveAt(0);
        }
    }

    public async Task InitializeAsync()
    {
        try
        {
            AddLogMessage("Initializing application...");

            // Update download button states initially
            UpdateDownloadButtonStates();

            // Try to load stored credentials
            var hasStoredCredentials = await _authService.LoadStoredCredentialsAsync();
            if (hasStoredCredentials)
            {
                AddLogMessage("Stored credentials loaded successfully");
            }
            else
            {
                AddLogMessage("No stored credentials found");
            }

            AddLogMessage("Application initialization completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize application");
            AddLogMessage($"Initialization error: {ex.Message}");
            StatusMessage = $"Initialization failed: {ex.Message}";

            // Show error to user
            MessageBox.Show($"Application initialization failed: {ex.Message}\n\nPlease check your configuration and try again.",
                "Initialization Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ShowDownloadSummary(DownloadSummary summary)
    {
        var message = $"Download completed!\n\n" +
                     $"Status: {summary.FinalStatus}\n" +
                     $"Total Photos: {summary.Statistics.TotalPhotos}\n" +
                     $"Downloaded: {summary.Statistics.DownloadedPhotos}\n" +
                     $"Failed: {summary.Statistics.FailedPhotos}\n" +
                     $"Total Time: {FormatTimeSpan(summary.TotalDuration)}\n" +
                     $"Average Speed: {(summary.Statistics.AverageDownloadSpeed.HasValue ? FormatBytes(summary.Statistics.AverageDownloadSpeed.Value) + "/s" : "N/A")}";

        if (summary.Errors.Any())
        {
            message += $"\n\nErrors: {summary.Errors.Count}";
        }

        MessageBox.Show(message, "Download Complete", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private static string FormatBytes(double bytes)
    {
        string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
        int counter = 0;
        decimal number = (decimal)bytes;
        while (Math.Round(number / 1024) >= 1)
        {
            number /= 1024;
            counter++;
        }
        return $"{number:n1} {suffixes[counter]}";
    }

    private static string FormatTimeSpan(TimeSpan timeSpan)
    {
        if (timeSpan.TotalDays >= 1)
            return $"{timeSpan.Days}d {timeSpan.Hours}h {timeSpan.Minutes}m";
        if (timeSpan.TotalHours >= 1)
            return $"{timeSpan.Hours}h {timeSpan.Minutes}m {timeSpan.Seconds}s";
        if (timeSpan.TotalMinutes >= 1)
            return $"{timeSpan.Minutes}m {timeSpan.Seconds}s";
        return $"{timeSpan.Seconds}s";
    }

    /// <summary>
    /// Initialize the album collection view with filtering and sorting
    /// </summary>
    private void InitializeAlbumCollectionView()
    {
        AlbumsView = CollectionViewSource.GetDefaultView(Albums);
        AlbumsView.Filter = FilterAlbums;

        // Subscribe to property changes for search functionality
        PropertyChanged += OnPropertyChanged;
    }

    /// <summary>
    /// Handle property changes to update filtering
    /// </summary>
    private void OnPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(SearchText))
        {
            AlbumsView?.Refresh();
        }
    }

    /// <summary>
    /// Filter albums based on search text
    /// </summary>
    private bool FilterAlbums(object item)
    {
        if (item is not SelectableAlbum album) return false;
        if (string.IsNullOrWhiteSpace(SearchText)) return true;

        var searchLower = SearchText.ToLowerInvariant();
        return album.Name.ToLowerInvariant().Contains(searchLower) ||
               album.Description.ToLowerInvariant().Contains(searchLower) ||
               album.FullPath.ToLowerInvariant().Contains(searchLower);
    }

    /// <summary>
    /// Populate albums from folder structure
    /// </summary>
    private void PopulateAlbumsFromFolderStructure(FolderNode folderStructure)
    {
        if (UseTreeView)
        {
            PopulateTreeViewFromFolderStructure(folderStructure);
        }
        else
        {
            // Legacy flat view
            Albums.Clear();
            var allAlbums = new List<SelectableAlbum>();
            CollectAlbumsFromFolder(folderStructure, allAlbums);

            foreach (var album in allAlbums)
            {
                album.PropertyChanged += OnAlbumSelectionChanged;
                Albums.Add(album);
            }

            TotalAlbumCount = Albums.Count;
            UpdateSelectionStatistics();
            AddLogMessage($"✅ Populated {Albums.Count} albums for display");
        }
    }

    /// <summary>
    /// Populate tree view from folder structure
    /// </summary>
    private void PopulateTreeViewFromFolderStructure(FolderNode folderStructure)
    {
        AlbumTreeItems.Clear();

        // Ensure folder totals are calculated before creating tree view
        CalculateFolderTotals(folderStructure);

        // Debug: Log the folder structure hierarchy
        AddLogMessage($"🔍 DEBUG: Root folder structure:");
        LogFolderStructure(folderStructure, 0);

        // Create tree items from the folder structure
        // If the root folder has a meaningful name, include it; otherwise, show its children
        if (!string.IsNullOrEmpty(folderStructure.Name) && folderStructure.Name != "Root")
        {
            // Include the root folder in the tree
            var rootItem = CreateTreeViewItemFromFolder(folderStructure, null);
            // Auto-expand the root folder to show top-level folders
            rootItem.IsExpanded = true;
            AlbumTreeItems.Add(rootItem);
            AddLogMessage($"🌳 Added root folder to tree: {folderStructure.Name} (auto-expanded)");
        }
        else
        {
            // Skip the root folder and add its children directly
            var rootItem = CreateTreeViewItemFromFolder(folderStructure, null);
            foreach (var child in rootItem.Children)
            {
                // Auto-expand first level folders to show the high-level view
                if (child.IsFolder)
                {
                    child.IsExpanded = true;
                }
                AlbumTreeItems.Add(child);
            }
            AddLogMessage($"🌳 Added {rootItem.Children.Count} root children to tree (auto-expanded)");
        }

        // Update display texts after folder totals are calculated
        UpdateTreeViewDisplayTexts(AlbumTreeItems);

        // Count total albums for statistics
        var totalAlbums = CountTotalAlbumsInTree(AlbumTreeItems);
        TotalAlbumCount = totalAlbums;
        UpdateTreeViewSelectionStatistics();
        AddLogMessage($"✅ Populated tree view with {totalAlbums} albums");
    }

    /// <summary>
    /// Recursively collect albums from folder structure
    /// </summary>
    private void CollectAlbumsFromFolder(FolderNode folder, List<SelectableAlbum> albums)
    {
        // Add albums from this folder
        foreach (var albumInfo in folder.Albums)
        {
            var selectableAlbum = new SelectableAlbum
            {
                AlbumKey = albumInfo.AlbumKey,
                Name = albumInfo.Name,
                Description = albumInfo.Description,
                UrlName = albumInfo.UrlName,
                ImageCount = albumInfo.ImageCount,
                EstimatedSizeBytes = albumInfo.EstimatedSizeBytes,
                DateCreated = albumInfo.DateCreated,
                DateModified = albumInfo.DateModified,
                Privacy = albumInfo.Privacy,
                NodeId = albumInfo.NodeId,
                ParentNodeId = albumInfo.ParentNodeId,
                FullPath = albumInfo.FullPath,
                AllowDownloads = albumInfo.AllowDownloads,
                IsPrivate = !albumInfo.IsPublic,
                IsPasswordProtected = !string.IsNullOrEmpty(albumInfo.Privacy) && albumInfo.Privacy.Contains("Password")
            };

            albums.Add(selectableAlbum);
        }

        // Recursively process child folders
        foreach (var childFolder in folder.Children)
        {
            CollectAlbumsFromFolder(childFolder, albums);
        }
    }

    /// <summary>
    /// Create a tree view item from a folder node
    /// </summary>
    private TreeViewItemModel CreateTreeViewItemFromFolder(FolderNode folder, TreeViewItemModel? parent)
    {
        var folderItem = new TreeViewItemModel(TreeViewItemType.Folder)
        {
            Name = folder.Name,
            Parent = parent,
            FolderNode = folder
        };

        // Subscribe to property changes for selection handling
        folderItem.PropertyChanged += OnTreeViewItemPropertyChanged;

        // Add albums from this folder
        foreach (var albumInfo in folder.Albums)
        {
            var selectableAlbum = new SelectableAlbum
            {
                AlbumKey = albumInfo.AlbumKey,
                Name = albumInfo.Name,
                Description = albumInfo.Description,
                UrlName = albumInfo.UrlName,
                ImageCount = albumInfo.ImageCount,
                EstimatedSizeBytes = albumInfo.EstimatedSizeBytes,
                DateCreated = albumInfo.DateCreated,
                DateModified = albumInfo.DateModified,
                Privacy = albumInfo.Privacy,
                NodeId = albumInfo.NodeId,
                ParentNodeId = albumInfo.ParentNodeId,
                FullPath = albumInfo.FullPath,
                AllowDownloads = albumInfo.AllowDownloads,
                IsPrivate = !albumInfo.IsPublic,
                IsPasswordProtected = !string.IsNullOrEmpty(albumInfo.Privacy) && albumInfo.Privacy.Contains("Password")
            };

            var albumItem = new TreeViewItemModel(TreeViewItemType.Album)
            {
                Name = selectableAlbum.DisplayName,
                Parent = folderItem,
                Album = selectableAlbum
            };

            // Subscribe to property changes for selection handling
            albumItem.PropertyChanged += OnTreeViewItemPropertyChanged;
            selectableAlbum.PropertyChanged += OnAlbumSelectionChanged;

            folderItem.Children.Add(albumItem);
        }

        // Recursively add child folders
        foreach (var childFolder in folder.Children)
        {
            var childItem = CreateTreeViewItemFromFolder(childFolder, folderItem);
            folderItem.Children.Add(childItem);
        }

        return folderItem;
    }

    /// <summary>
    /// Handle tree view item property changes
    /// </summary>
    private void OnTreeViewItemPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(TreeViewItemModel.IsChecked))
        {
            if (sender is TreeViewItemModel item && item.Album != null)
            {
                // Sync album selection with tree view checkbox
                item.Album.IsSelected = item.IsChecked == true;
            }
            UpdateTreeViewSelectionStatistics();
        }
    }

    /// <summary>
    /// Handle album selection changes
    /// </summary>
    private void OnAlbumSelectionChanged(object? sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(SelectableAlbum.IsSelected))
        {
            if (UseTreeView)
            {
                UpdateTreeViewSelectionStatistics();
            }
            else
            {
                UpdateSelectionStatistics();
            }
        }
    }

    /// <summary>
    /// Update selection statistics
    /// </summary>
    private void UpdateSelectionStatistics()
    {
        var selectedAlbums = Albums.Where(a => a.IsSelected).ToList();
        SelectedAlbumCount = selectedAlbums.Count;
        TotalSelectedImageCount = selectedAlbums.Sum(a => a.ImageCount);

        var totalSizeBytes = selectedAlbums.Sum(a => a.EstimatedSizeBytes);
        TotalSelectedSize = FormatBytes(totalSizeBytes);

        // Update button states when selection changes - THIS WAS MISSING!
        UpdateDownloadButtonStates();

        // Debug logging for selection changes
        _logger.LogDebug("Selection statistics updated: {SelectedCount}/{TotalCount} albums selected, {TotalImages} images, {TotalSize}",
            SelectedAlbumCount, Albums.Count, TotalSelectedImageCount, TotalSelectedSize);

        // Also log current button state when selection changes
        LogCurrentButtonState();
    }

    /// <summary>
    /// Update selection statistics for tree view
    /// </summary>
    private void UpdateTreeViewSelectionStatistics()
    {
        var selectedAlbums = GetSelectedAlbumsFromTree(AlbumTreeItems);
        SelectedAlbumCount = selectedAlbums.Count;
        TotalSelectedImageCount = selectedAlbums.Sum(a => a.ImageCount);

        var totalSizeBytes = selectedAlbums.Sum(a => a.EstimatedSizeBytes);
        TotalSelectedSize = FormatBytes(totalSizeBytes);

        // Update button states when selection changes
        UpdateDownloadButtonStates();

        // Debug logging for selection changes
        _logger.LogDebug("Tree view selection statistics updated: {SelectedCount}/{TotalCount} albums selected, {TotalImages} images, {TotalSize}",
            SelectedAlbumCount, TotalAlbumCount, TotalSelectedImageCount, TotalSelectedSize);
    }

    /// <summary>
    /// Get all selected albums from tree view
    /// </summary>
    private List<SelectableAlbum> GetSelectedAlbumsFromTree(IEnumerable<TreeViewItemModel> items)
    {
        var selectedAlbums = new List<SelectableAlbum>();

        foreach (var item in items)
        {
            if (item.IsAlbum && item.Album != null && item.Album.IsSelected)
            {
                selectedAlbums.Add(item.Album);
            }

            // Recursively check children
            selectedAlbums.AddRange(GetSelectedAlbumsFromTree(item.Children));
        }

        return selectedAlbums;
    }

    /// <summary>
    /// Count total albums in tree view
    /// </summary>
    private int CountTotalAlbumsInTree(IEnumerable<TreeViewItemModel> items)
    {
        int count = 0;

        foreach (var item in items)
        {
            if (item.IsAlbum)
            {
                count++;
            }

            // Recursively count children
            count += CountTotalAlbumsInTree(item.Children);
        }

        return count;
    }

    /// <summary>
    /// Log the current state of all button-related properties for debugging
    /// </summary>
    [RelayCommand]
    private void LogCurrentButtonState()
    {
        var status = _downloadManager.Status;
        _logger.LogDebug("=== BUTTON STATE DEBUG ===");
        _logger.LogDebug("CanStartDownload: {CanStartDownload}", CanStartDownload);
        _logger.LogDebug("IsAuthenticated: {IsAuthenticated}", IsAuthenticated);
        _logger.LogDebug("TargetDirectory: {TargetDirectory}", TargetDirectory ?? "null");
        _logger.LogDebug("DownloadManager.Status: {Status}", status);
        _logger.LogDebug("IsDownloading: {IsDownloading}", IsDownloading);
        _logger.LogDebug("IsOperationInProgress: {IsOperationInProgress}", IsOperationInProgress);
        _logger.LogDebug("Albums.Count: {AlbumCount}", Albums.Count);
        _logger.LogDebug("SelectedAlbumCount: {SelectedCount}", SelectedAlbumCount);
        _logger.LogDebug("========================");

        AddLogMessage($"🔍 BUTTON STATE: Enabled={CanStartDownload}, Auth={IsAuthenticated}, Dir={!string.IsNullOrEmpty(TargetDirectory)}, Status={status}, Selected={SelectedAlbumCount}");
    }

    /// <summary>
    /// Select all albums
    /// </summary>
    [RelayCommand]
    private void SelectAllAlbums()
    {
        if (UseTreeView)
        {
            SelectAllAlbumsInTree(AlbumTreeItems, true);
        }
        else
        {
            foreach (var album in Albums)
            {
                album.IsSelected = true;
            }
        }
    }

    /// <summary>
    /// Deselect all albums
    /// </summary>
    [RelayCommand]
    private void DeselectAllAlbums()
    {
        if (UseTreeView)
        {
            SelectAllAlbumsInTree(AlbumTreeItems, false);
        }
        else
        {
            foreach (var album in Albums)
            {
                album.IsSelected = false;
            }
        }
    }

    /// <summary>
    /// Select or deselect all albums in tree view
    /// </summary>
    private void SelectAllAlbumsInTree(IEnumerable<TreeViewItemModel> items, bool isSelected)
    {
        foreach (var item in items)
        {
            if (item.IsAlbum && item.Album != null)
            {
                item.Album.IsSelected = isSelected;
                item.IsChecked = isSelected;
            }
            else if (item.IsFolder)
            {
                item.IsChecked = isSelected;
            }

            // Recursively process children
            SelectAllAlbumsInTree(item.Children, isSelected);
        }
    }

    /// <summary>
    /// Expand all folders in tree view
    /// </summary>
    [RelayCommand]
    private void ExpandAllFolders()
    {
        if (UseTreeView)
        {
            ExpandAllFoldersInTree(AlbumTreeItems, true);
            AddLogMessage("All folders expanded");
        }
    }

    /// <summary>
    /// Collapse all folders in tree view
    /// </summary>
    [RelayCommand]
    private void CollapseAllFolders()
    {
        if (UseTreeView)
        {
            ExpandAllFoldersInTree(AlbumTreeItems, false);
            AddLogMessage("All folders collapsed");
        }
    }

    /// <summary>
    /// Expand or collapse all folders in tree view
    /// </summary>
    private void ExpandAllFoldersInTree(IEnumerable<TreeViewItemModel> items, bool isExpanded)
    {
        foreach (var item in items)
        {
            if (item.IsFolder)
            {
                item.IsExpanded = isExpanded;
            }

            // Recursively process children
            ExpandAllFoldersInTree(item.Children, isExpanded);
        }
    }

    /// <summary>
    /// Calculate totals for a folder and all its children (recursive)
    /// </summary>
    private void CalculateFolderTotals(FolderNode folder)
    {
        // Start with direct albums in this folder
        folder.TotalImageCount = folder.Albums.Sum(a => a.ImageCount);
        folder.TotalEstimatedSizeBytes = folder.Albums.Sum(a => a.EstimatedSizeBytes);

        // Recursively calculate for children and add to totals
        foreach (var child in folder.Children)
        {
            CalculateFolderTotals(child);
            folder.TotalImageCount += child.TotalImageCount;
            folder.TotalEstimatedSizeBytes += child.TotalEstimatedSizeBytes;
        }
    }

    /// <summary>
    /// Update display text for all tree view items after folder totals are calculated
    /// </summary>
    private void UpdateTreeViewDisplayTexts(IEnumerable<TreeViewItemModel> items)
    {
        foreach (var item in items)
        {
            // Force update of display text by triggering property change notifications
            if (item.IsFolder)
            {
                // Trigger property change for ImageCount and EstimatedSizeBytes to update DisplayText
                item.OnPropertyChanged(nameof(item.ImageCount));
                item.OnPropertyChanged(nameof(item.EstimatedSizeBytes));
            }

            // Recursively update children
            UpdateTreeViewDisplayTexts(item.Children);
        }
    }

    /// <summary>
    /// Debug method to log folder structure hierarchy
    /// </summary>
    private void LogFolderStructure(FolderNode folder, int depth)
    {
        var indent = new string(' ', depth * 2);
        AddLogMessage($"{indent}📁 {folder.Name} (Path: '{folder.FullPath}') - {folder.Albums.Count} albums, {folder.Children.Count} subfolders - {folder.TotalImageCount} images");

        // Log albums in this folder with their full paths
        foreach (var album in folder.Albums)
        {
            AddLogMessage($"{indent}  📸 {album.Name} (Path: '{album.FullPath}') - {album.ImageCount} images");
        }

        // Recursively log child folders
        foreach (var child in folder.Children)
        {
            LogFolderStructure(child, depth + 1);
        }
    }

    /// <summary>
    /// Load user profile information including display name and profile picture
    /// </summary>
    private async Task LoadUserProfileAsync()
    {
        try
        {
            AddLogMessage("📡 Loading user profile information...");

            // Get authenticated user information
            var user = await Task.Run(() => _apiClient.GetAuthenticatedUser());
            CurrentUser = user;

            // Set display name with detailed logging
            AddLogMessage($"📋 User data - Name: '{user.Name}', DisplayName: '{user.DisplayName}', FirstName: '{user.FirstName}', LastName: '{user.LastName}', NickName: '{user.NickName}'");

            UserDisplayName = !string.IsNullOrEmpty(user.Name) ? user.Name :
                             !string.IsNullOrEmpty(user.DisplayName) ? user.DisplayName :
                             !string.IsNullOrEmpty(user.FirstName) && !string.IsNullOrEmpty(user.LastName)
                                 ? $"{user.FirstName} {user.LastName}" :
                             !string.IsNullOrEmpty(user.NickName) ? user.NickName : "Unknown User";

            AddLogMessage($"📋 Final UserDisplayName set to: '{UserDisplayName}'");

            // Try to get profile picture from SmugMug bio image API
            await LoadUserProfileImageAsync(user.NickName);

            // Update status text
            UserStatusText = "Status: ready to start downloading";

            AddLogMessage($"✅ User profile loaded: {UserDisplayName}");

            if (user.ImageCount.HasValue)
            {
                AddLogMessage($"📊 Total images in account: {user.ImageCount.Value:N0}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load user profile");
            AddLogMessage($"❌ Failed to load user profile: {ex.Message}");

            // Set fallback values
            UserDisplayName = UserNickname ?? "Unknown User";
            UserProfileImageUrl = null;
            HasProfileImage = false;
            UserStatusText = "Status: ready to start downloading";

            // Try to load profile image even on error, using nickname
            if (!string.IsNullOrEmpty(UserNickname))
            {
                _ = Task.Run(async () => await LoadUserProfileImageAsync(UserNickname));
            }
        }
    }

    /// <summary>
    /// Load user profile image from SmugMug bio image API
    /// </summary>
    private async Task LoadUserProfileImageAsync(string nickname)
    {
        try
        {
            if (string.IsNullOrEmpty(nickname))
            {
                AddLogMessage("📸 No nickname available, using initials fallback");
                UserProfileImageUrl = null;
                HasProfileImage = false;
                return;
            }

            // Call the bio image API endpoint
            var bioImageUrl = $"{BaseApiUrl}/user/{nickname}!bioimage";
            AddLogMessage($"📸 Calling bio image API: {bioImageUrl}");

            // Create authenticated request
            var request = _authService.CreateAuthenticatedRequest(HttpMethod.Get, bioImageUrl);
            request.Headers.Add("Accept", "application/json");

            using var httpClient = new HttpClient();
            var response = await httpClient.SendAsync(request);

            if (response.IsSuccessStatusCode)
            {
                var jsonContent = await response.Content.ReadAsStringAsync();
                AddLogMessage($"📸 Bio image API response received");

                // Parse the JSON response
                var jsonDoc = JsonDocument.Parse(jsonContent);
                var root = jsonDoc.RootElement;

                // Look for the bio image in the response
                if (root.TryGetProperty("Response", out var responseElement) &&
                    responseElement.TryGetProperty("BioImage", out var bioImageElement))
                {
                    // Try to get different image sizes
                    string? imageUrl = null;

                    // Check for Uris.ImageSizes
                    if (bioImageElement.TryGetProperty("Uris", out var urisElement) &&
                        urisElement.TryGetProperty("ImageSizes", out var imageSizesElement) &&
                        imageSizesElement.TryGetProperty("Uri", out var imageSizesUriElement))
                    {
                        var imageSizesUri = imageSizesUriElement.GetString();
                        if (!string.IsNullOrEmpty(imageSizesUri))
                        {
                            // Make another call to get the actual image URLs
                            var sizesUrl = imageSizesUri.StartsWith("http") ? imageSizesUri : $"{BaseApiUrl.TrimEnd('/')}{imageSizesUri}";
                            var sizesRequest = _authService.CreateAuthenticatedRequest(HttpMethod.Get, sizesUrl);
                            sizesRequest.Headers.Add("Accept", "application/json");

                            var sizesResponse = await httpClient.SendAsync(sizesRequest);
                            if (sizesResponse.IsSuccessStatusCode)
                            {
                                var sizesJsonContent = await sizesResponse.Content.ReadAsStringAsync();
                                var sizesJsonDoc = JsonDocument.Parse(sizesJsonContent);
                                var sizesRoot = sizesJsonDoc.RootElement;

                                if (sizesRoot.TryGetProperty("Response", out var sizesResponseElement) &&
                                    sizesResponseElement.TryGetProperty("ImageSizes", out var imageSizesResponseElement))
                                {
                                    // Try to get a medium-sized image (good for profile pictures)
                                    if (imageSizesResponseElement.TryGetProperty("MediumImageUrl", out var mediumElement))
                                        imageUrl = mediumElement.GetString();
                                    else if (imageSizesResponseElement.TryGetProperty("SmallImageUrl", out var smallElement))
                                        imageUrl = smallElement.GetString();
                                    else if (imageSizesResponseElement.TryGetProperty("ThumbImageUrl", out var thumbElement))
                                        imageUrl = thumbElement.GetString();
                                    else if (imageSizesResponseElement.TryGetProperty("TinyImageUrl", out var tinyElement))
                                        imageUrl = tinyElement.GetString();
                                }
                            }
                        }
                    }

                    // If we found an image URL, use it
                    if (!string.IsNullOrEmpty(imageUrl))
                    {
                        UserProfileImageUrl = imageUrl;
                        HasProfileImage = true;
                        AddLogMessage("✅ SmugMug profile picture loaded successfully");
                        return;
                    }
                }

                AddLogMessage("📸 No profile image found in bio image response, using initials");
            }
            else
            {
                AddLogMessage($"📸 Bio image API call failed: {response.StatusCode} - {response.ReasonPhrase}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to load profile picture from bio image API");
            AddLogMessage($"📸 Profile picture API failed: {ex.Message}");
        }

        // Fallback to initials
        UserProfileImageUrl = null;
        HasProfileImage = false;
        AddLogMessage("📸 Using initials fallback for profile display");
    }

    private const string BaseApiUrl = "https://api.smugmug.com/api/v2";
}
