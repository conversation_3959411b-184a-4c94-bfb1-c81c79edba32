using Microsoft.Extensions.Logging;

namespace Winmug.Core.Authentication;

/// <summary>
/// Simple console logger for OAuth callback server
/// </summary>
public class ConsoleLogger<T> : ILogger<T>
{
    public IDisposable? BeginScope<TState>(TState state) where TState : notnull
    {
        return null;
    }

    public bool IsEnabled(LogLevel logLevel)
    {
        return logLevel >= LogLevel.Information;
    }

    public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
    {
        if (!IsEnabled(logLevel))
            return;

        var message = formatter(state, exception);
        var timestamp = DateTime.Now.ToString("HH:mm:ss");
        var levelString = logLevel switch
        {
            LogLevel.Information => "INFO",
            LogLevel.Warning => "WARN",
            LogLevel.Error => "ERROR",
            LogLevel.Debug => "DEBUG",
            _ => logLevel.ToString().ToUpper()
        };

        Console.WriteLine($"[{timestamp}] [{levelString}] {typeof(T).Name}: {message}");
        
        if (exception != null)
        {
            Console.WriteLine($"[{timestamp}] [ERROR] Exception: {exception}");
        }
    }
}
